{"name": "my-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "prepare": "husky install", "lint:check": "eslint . -f table", "lint:fix": "eslint --fix -f codeframe", "format:check": "prettier --check .", "format:write": "prettier --write ."}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix -f codeframe", "prettier --write"]}, "dependencies": {"@heygen/streaming-avatar": "^2.0.16", "@hookform/resolvers": "^3.10.0", "@huggingface/transformers": "^3.5.1", "@mediapipe/face_mesh": "^0.4.1633559619", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.5.1", "@shadcn/ui": "^0.0.4", "@stitches/react": "^1.2.8", "@tabler/icons-react": "^3.29.0", "@tanstack/react-table": "^8.20.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clmtrackr": "^1.1.2", "clsx": "^2.1.1", "cookie": "^1.0.2", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "file-type": "^21.0.0", "formidable": "^3.5.2", "framer-motion": "^11.18.1", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "livekit-client": "^2.13.3", "lottie-react": "^2.4.1", "lucide-react": "^0.473.0", "next": "^15.1.5", "next-themes": "^0.4.4", "npm": "^11.0.0", "protobufjs": "^6.6.4", "react": "^18.3.1", "react-avatar": "^5.0.3", "react-datepicker": "^8.0.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-mic": "^12.4.6", "react-redux": "^9.2.0", "react-visibility-sensor": "^5.1.1", "react-webcam": "^7.2.0", "redux-persist": "^6.0.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.28.0", "@next/eslint-plugin-next": "^15.3.3", "@types/clmtrackr": "^1.1.3", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-formatter-codeframe": "^7.32.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "husky": "^8.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5", "typescript-eslint": "^8.34.0"}}