const formatNumber = (num: number): string => {
    if (num >= 1_000_000_000_000) {
        return `${(num / 1_000_000_000_000).toFixed(1).replace(/\.0$/, '')}T`
    } else if (num >= 1_000_000_000) {
        return `${(num / 1_000_000_000).toFixed(1).replace(/\.0$/, '')}B`
    } else if (num >= 1_000_000) {
        return `${(num / 1_000_000).toFixed(1).replace(/\.0$/, '')}M`
    } else if (num >= 1_000) {
        return `${(num / 1_000).toFixed(1).replace(/\.0$/, '')}K`
    } else {
        return num.toString()
    }
}

export function formatSalaryRange(
    min: number | string | null | undefined,
    max: number | string | null | undefined,
): string {
    if (min == null || max == null) return 'No annual salary specified'
    // const minNum = typeof min === 'string' ? parseInt(min, 10) : min;
    // const maxNum = typeof max === 'string' ? parseInt(max, 10) : max;
    const minNum = typeof min === 'string' ? parseFloat(min) : min
    const maxNum = typeof max === 'string' ? parseFloat(max) : max
    if (isNaN(minNum) || isNaN(maxNum)) return 'No annual salary specified'
    return `${minNum.toLocaleString('en-IN')} - ${maxNum.toLocaleString('en-IN')} LPA`
}

export function formatWithCommas(
    num: number | string | null | undefined,
): string {
    if (num == null || num === '') return '0'
    const n = typeof num === 'string' ? parseInt(num, 10) : num
    if (isNaN(n)) return '0'
    return n.toLocaleString('en-IN')
}

export default formatNumber
