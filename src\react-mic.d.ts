declare module 'react-mic' {
    import * as React from 'react'
    export interface ReactMicProps {
        record: boolean
        className?: string
        onStop?: (recordedData: { blobURL: string }) => void
        strokeColor?: string
        backgroundColor?: string
        mimeType?: string
        onData?: (recordedData: any) => void
        visualSetting?: string
        echoCancellation?: boolean
        channelCount?: number
        autoGainControl?: boolean
        noiseSuppression?: boolean
        sampleRate?: number
        bitRate?: number
        width?: number
        height?: number
    }
    export class ReactMic extends React.Component<ReactMicProps> {}
}
