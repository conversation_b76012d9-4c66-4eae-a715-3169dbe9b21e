'use client'

import DashboardLayout from '../dashboard-layout'
import UserInfoCard from './user-info-card'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { setLogedinUser } from '@/redux/constSlice'
import InterviewListPage from '@/components/interviews/page'
import type { Candidate } from '@/Models/candidate'
import { useCandidateService } from '@/services/candidate.service'

export default function Page() {
    const [user, setUser] = useState<Candidate | null>(null)
    const dispatch = useDispatch()
    const { fetchCandidateDetails } = useCandidateService()

    useEffect(() => {
        const fetchUserData = async () => {
            try {
                const resData = await fetchCandidateDetails()
                setUser(resData)
                dispatch(setLogedinUser(resData))
            } catch (err) {
                console.error('Error fetching user data:', err)
            }
        }

        fetchUserData()
    }, [dispatch, fetchCandidateDetails])

    return (
        <DashboardLayout>
            {user && (
                <UserInfoCard
                    name={user.candidateName}
                    mobileNo={user.candidateMobile}
                />
            )}
            <InterviewListPage />
        </DashboardLayout>
    )
}
