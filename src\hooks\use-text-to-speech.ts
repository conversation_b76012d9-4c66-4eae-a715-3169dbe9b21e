'use client'

import { useState, useCallback } from 'react'

export const useTextToSpeech = () => {
    const [isSpeaking, setIsSpeaking] = useState(false)

    const speak = useCallback((text: string, onEnd?: () => void) => {
        if (typeof window === 'undefined' || !('speechSynthesis' in window)) {
            console.warn('Text-to-speech not supported')
            onEnd?.()
            return
        }

        window.speechSynthesis.cancel()

        const utterance = new SpeechSynthesisUtterance(text)
        utterance.pitch = 1
        utterance.rate = 0.9
        utterance.volume = 1

        setIsSpeaking(true)

        utterance.onend = () => {
            setIsSpeaking(false)
            onEnd?.()
        }

        utterance.onerror = () => {
            setIsSpeaking(false)
            onEnd?.()
        }

        window.speechSynthesis.speak(utterance)
    }, [])

    const stopSpeaking = useCallback(() => {
        if (typeof window !== 'undefined' && window.speechSynthesis) {
            window.speechSynthesis.cancel()
            setIsSpeaking(false)
        }
    }, [])

    return { speak, stopSpeaking, isSpeaking }
}
