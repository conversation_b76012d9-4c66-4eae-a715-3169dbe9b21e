'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { IconCheck } from '@tabler/icons-react'

const LottieSuccess = () => (
    <motion.div
        className="relative w-20 h-20"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
            type: 'spring',
            stiffness: 300,
            damping: 20,
            delay: 0.3,
        }}
    >
        <motion.div
            className="w-full h-full bg-green-500 rounded-full flex items-center justify-center shadow-lg"
            initial={{ rotate: -90 }}
            animate={{ rotate: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut', delay: 0.4 }}
        >
            <IconCheck className="w-10 h-10 text-white stroke-[3]" />
        </motion.div>

        {/* Soft glow/pulse effect */}
        <motion.div
            className="absolute inset-0 rounded-full bg-green-400 opacity-50"
            initial={{ scale: 1, opacity: 0.5 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{
                duration: 1.5,
                repeat: Infinity,
                repeatType: 'loop',
                ease: 'easeOut',
            }}
        />
    </motion.div>
)

export default function SuccessCard() {
    return (
        <div className="min-h-screen bg-gray-100 flex items-center justify-center p-6">
            <motion.div
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                    type: 'spring',
                    stiffness: 200,
                    damping: 25,
                    duration: 0.6,
                }}
            >
                <Card className="w-full max-w-3xl mx-auto bg-white rounded-2xl shadow-xl border-0">
                    <CardContent className="py-16 px-8 md:px-16 text-center relative">
                        {/* Success Icon */}
                        <div className="flex justify-center mb-10">
                            <LottieSuccess />
                        </div>

                        {/* Title */}
                        <motion.h2
                            className="text-2xl md:text-3xl font-bold text-gray-900 mb-4"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.8, duration: 0.4 }}
                        >
                            Interview Completed
                        </motion.h2>

                        {/* Description */}
                        <motion.p
                            className="text-gray-600 md:text-lg leading-relaxed max-w-xl mx-auto"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 1.0, duration: 0.4 }}
                        >
                            Thank you for your time. Our team will review your
                            responses and get back to you shortly.
                        </motion.p>

                        {/* Floating Particle Animations */}
                        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
                            {[...Array(8)].map((_, i) => (
                                <motion.div
                                    key={i}
                                    className="absolute w-2 h-2 bg-green-400 rounded-full opacity-30"
                                    initial={{
                                        x: Math.random() * 500,
                                        y: Math.random() * 300,
                                        scale: 0,
                                    }}
                                    animate={{
                                        y: [null, -20, -60],
                                        scale: [0, 1, 0],
                                        opacity: [0, 0.6, 0],
                                    }}
                                    transition={{
                                        duration: 2.5,
                                        delay: 1 + i * 0.3,
                                        repeat: Infinity,
                                        repeatDelay: 3,
                                    }}
                                />
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </motion.div>
        </div>
    )
}
