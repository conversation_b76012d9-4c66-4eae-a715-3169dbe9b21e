'use client'

import { useState, useRef } from 'react'
import { CheckCircle2, Mic } from 'lucide-react'

interface MicrophoneTestProps {
    onSuccess: () => void
}

export default function MicrophoneTest({ onSuccess }: MicrophoneTestProps) {
    const [recording, setRecording] = useState(false)
    const [success, setSuccess] = useState(false)
    const [error, setError] = useState('')
    const recognitionRef = useRef<any>(null)

    const expectedText = 'hello my name is john'

    const handleStart = () => {
        setRecording(true)
        setError('')
        const SpeechRecognition =
            (window as any).SpeechRecognition ||
            (window as any).webkitSpeechRecognition
        if (!SpeechRecognition) {
            setError('Speech recognition is not supported in this browser.')
            setRecording(false)
            return
        }
        const recognition = new SpeechRecognition()
        recognition.lang = 'en-US'
        recognition.interimResults = false
        recognition.maxAlternatives = 1
        recognition.onresult = (event: any) => {
            const transcript = event.results[0][0].transcript
                .toLowerCase()
                .trim()
            if (transcript === expectedText) {
                setSuccess(true)
                setTimeout(() => onSuccess(), 1500)
            } else {
                setError('Text did not match. Please try again.')
            }
            setRecording(false)
        }
        recognition.onerror = () => {
            setError('Could not recognize speech. Please try again.')
            setRecording(false)
        }
        recognitionRef.current = recognition
        recognition.start()
    }

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white p-8 rounded shadow-lg flex flex-col items-center min-w-[350px]">
                {!success ? (
                    <>
                        <h2 className="text-lg font-semibold mb-2 text-center">
                            Check Test Voice and Read the text below ?
                        </h2>
                        <div className="mb-4 text-center text-base">
                            Hello, My name is John
                        </div>
                        <Mic className="h-10 w-10 text-blue-700 mb-4" />
                        <button
                            className="px-6 py-2 bg-blue-900 text-white rounded"
                            onClick={handleStart}
                            disabled={recording}
                        >
                            {recording ? 'Listening...' : 'Test Voice'}
                        </button>
                        {error && (
                            <div className="text-red-600 mt-2">{error}</div>
                        )}
                    </>
                ) : (
                    <div className="flex flex-col items-center justify-center">
                        <CheckCircle2 className="h-16 w-16 text-green-600 mb-4" />
                        <div className="text-lg font-semibold mb-2">
                            Voice tested successfully
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
