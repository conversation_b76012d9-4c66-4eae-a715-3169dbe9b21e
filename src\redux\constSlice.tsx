import type { PayloadAction } from '@reduxjs/toolkit'
import { createSlice } from '@reduxjs/toolkit'
import type { Candidate } from '@/Models/candidate'
import type { EnumItem } from '@/Models/enums'

interface DropdownEnumValues {
    skillTypes: EnumItem[]
    skillLevel: EnumItem[]
    interviewStatus: EnumItem[]
    subscriptionTypes: EnumItem[]
    transactionTypes: EnumItem[]
    interviewTypes: EnumItem[]
    workModes: EnumItem[]
}

interface ConstState {
    selectedEmail: string
    logedinUser: Candidate | null

    DropdownEnumValues: DropdownEnumValues
}

const initialState: ConstState = {
    selectedEmail: '',
    DropdownEnumValues: {
        skillTypes: [],
        interviewStatus: [],
        subscriptionTypes: [],
        transactionTypes: [],
        interviewTypes: [],
        skillLevel: [],
        workModes: [],
    },

    logedinUser: null,
}

const constSlice = createSlice({
    name: 'constant',
    initialState,
    reducers: {
        setSelectedEmail: (state, action: PayloadAction<string>) => {
            state.selectedEmail = action.payload
        },
        setLogedinUser: (state, action: PayloadAction<Candidate | null>) => {
            state.logedinUser = action.payload
        },
        setDropdownEnumValues: (
            state,
            action: PayloadAction<DropdownEnumValues>,
        ) => {
            state.DropdownEnumValues = action.payload
        },
    },
})

export const { setSelectedEmail, setLogedinUser, setDropdownEnumValues } =
    constSlice.actions

export default constSlice.reducer
