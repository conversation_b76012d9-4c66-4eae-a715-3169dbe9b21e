import { useEffect, useState } from 'react'

/**
 * Debounce any changing value and delay updating it until after `delay` ms.
 *
 * @param value - The input value to debounce.
 * @param delay - Delay time in milliseconds.
 * @returns The debounced value.
 */
export function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState(value)

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value)
        }, delay)

        return () => {
            clearTimeout(handler)
        }
    }, [value, delay])

    return debouncedValue
}
