import type { <PERSON>ada<PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>o } from 'next/font/google'
import './globals.css'
import { Poppins } from 'next/font/google'
const LazyClientProvider = React.lazy(() => import('./provider'))

import { Toaster } from '@/components/ui/sonner'
import React, { Suspense } from 'react'

const inter = Poppins({
    subsets: ['latin'],
    weight: '500',
})
export const metadata: Metadata = {
    title: 'Interview',
    description: 'Generated by create next app',
}

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang="en">
            <body
                className={`${inter.className} bg-neutral-100 dark:bg-[#000000] overflow-hidden`}
            >
                <Suspense fallback={<div>Loading application...</div>}>
                    <LazyClientProvider>{children}</LazyClientProvider>
                </Suspense>
                <Toaster />
            </body>
        </html>
    )
}
