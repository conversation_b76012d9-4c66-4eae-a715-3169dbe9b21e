'use client'

import { memo, forwardRef } from 'react'
import { MoreHorizontal } from 'lucide-react'

interface VideoSectionProps {
    userName: string
}

const VideoSection = memo(
    forwardRef<HTMLVideoElement, VideoSectionProps>(({ userName }, ref) => {
        // Add error handling for video element
        const handleVideoError = (error: any) => {
            console.error('Video element error:', error)
        }

        return (
            <div className="relative rounded-xl overflow-hidden bg-gray-900 aspect-[4/3] shadow-lg">
                <video
                    ref={ref}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                    onError={handleVideoError}
                    onLoadedMetadata={() =>
                        console.warn('Video metadata loaded')
                    }
                />
                <div className="absolute top-4 left-4">
                    <div className="bg-black/60 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm font-medium">
                        {userName}
                    </div>
                </div>
                <div className="absolute bottom-4 right-4">
                    <button className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors">
                        <MoreHorizontal className="h-5 w-5" />
                    </button>
                </div>
            </div>
        )
    }),
)

VideoSection.displayName = 'VideoSection'

export default VideoSection
