'use client'

import type { FormEvent } from 'react'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter, useSearchParams } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { setSelectedEmail } from '@/redux/constSlice'
import { useAuthService } from '@/services/auth.service'
import FormInput from '../constant/FormInput'

interface FormData {
    email: string
}

interface ResetPasswordComponentProps {
    onBack: () => void
}

const EmailVerificationComponent: React.FC<ResetPasswordComponentProps> = ({
    onBack,
}) => {
    const [formData, setFormData] = useState<FormData>({ email: '' })
    const [loading, setLoading] = useState<boolean>(false)
    const dispatch = useDispatch()
    const searchParams = useSearchParams()
    const router = useRouter()

    const encodedEmail = searchParams.get('emailId')
    const interviewID = searchParams.get('id')
    const email = encodedEmail ? atob(encodedEmail) : null

    const { sendOtp } = useAuthService()

    useEffect(() => {
        if (email) {
            setFormData((prev) => ({ ...prev, email }))
        }
    }, [email])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))
    }

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault()
        setLoading(true)
        dispatch(setSelectedEmail(formData.email))

        const result = await sendOtp(formData.email)
        if (result.success) {
            if (interviewID) {
                router.push(`/otpVerification?interviewId=${interviewID}`)
            } else {
                router.push('/otpVerification')
            }
        }

        setLoading(false)
    }

    return (
        <motion.div
            className="flex justify-center items-center min-h-screen p-4 sm:p-8 bg-[#F8F8FF] dark:bg-black"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
        >
            <motion.div
                className="dark:text-white p-6 sm:p-8 rounded-lg w-full max-w-md sm:max-w-lg flex flex-col items-start"
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3 }}
            >
                <motion.h2
                    className="text-left mb-4 sm:mb-6 text-xl sm:text-2xl md:text-3xl font-bold w-full"
                    style={{
                        fontFamily: 'Poppins, sans-serif',
                        textUnderlinePosition: 'from-font',
                        textDecorationSkipInk: 'none',
                    }}
                    initial={{ y: -20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                >
                    Sign In
                </motion.h2>

                <form onSubmit={handleSubmit} className="w-full">
                    <motion.div
                        className="mb-4"
                        initial={{ opacity: 0, x: -50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                    >
                        <FormInput
                            label="Email Id"
                            required
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            placeholder="Enter Your Email Id"
                        />
                    </motion.div>

                    <motion.button
                        type="submit"
                        className="w-full sm:w-[20rem] bg-[#073071] text-white py-2 sm:py-3 rounded-md disabled:bg-blue-300 dark:bg-blue-700"
                        disabled={loading || !formData.email}
                        whileHover={{
                            scale: 1.05,
                            transition: {
                                type: 'spring',
                                stiffness: 300,
                                damping: 20,
                            },
                        }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.8, duration: 0.5 }}
                    >
                        {loading ? 'Sending OTP...' : 'Continue'}
                    </motion.button>
                </form>
            </motion.div>
        </motion.div>
    )
}

export default EmailVerificationComponent
