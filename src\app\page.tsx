'use client'
import InitialLayout from './initial-layout'

import { useState } from 'react'

import EmailVerficationComponent from '@/components/emilaverification/page'

export default function Home() {
    const [isResetPassword, setIsResetPassword] = useState(false)

    return (
        <InitialLayout>
            <div className="relative w-full h-full flex items-center justify-center ">
                {/* <AnimatePresence mode="wait"> */}
                <div
                    key={isResetPassword ? 'reset-password' : 'login'}
                    // initial="hidden"
                    // animate="visible"
                    // exit="exit"
                    // variants={formVariants}
                    // transition={{ duration: 0.5, type: 'spring' }} // Smooth transition effect
                    className="relative w-full max-w-md" // Ensure form stays on the right half
                >
                    <EmailVerficationComponent
                        onBack={() => setIsResetPassword(false)}
                    />
                </div>
                {/* </AnimatePresence> */}
            </div>
        </InitialLayout>
    )
}
