# Project H Interview Web App

This is a modern web application built with Next.js and React that provides a platform for candidates to sign in, browse job interviews, and participate in AI-powered live interviews with video, audio, and real-time transcription features.

## Features

- **Email OTP Authentication:** Secure sign-in using email and OTP verification with JWT token management.
- **Candidate Dashboard:** View user information and browse available job interviews with real-time data.
- **Job Listings:** Searchable and paginated job interview listings with infinite scroll and advanced filtering.
- **AI-Powered Interviews:** Participate in live interviews with audio questions, video recording, speech recognition, and real-time transcript display.
- **Screen Sharing:** Candidates are required to share their entire screen during interviews for proctoring.
- **Interview Controls:** Skip, complete, or end interviews with smooth UI controls and time management.
- **Resume & JD Viewer:** View resumes and job descriptions directly in the browser with PDF support.
- **Responsive UI:** Built with Tailwind CSS and Radix UI components for a modern and accessible interface.
- **State Management:** Uses Redux Toolkit for global state management with persistent user sessions.
- **Real-time Communication:** WebSocket integration for live interview question and transcript streaming.
- **Toast Notifications:** User-friendly notifications using Sonner for better UX.

## Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **React 18** - UI library with hooks and modern patterns
- **TypeScript** - Type-safe development
- **Redux Toolkit** - State management with RTK Query
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Framer Motion** - Animation library
- **Sonner** - Toast notifications

### Backend Integration
- **Axios** - HTTP client for API communication
- **WebSocket** - Real-time communication
- **JWT** - Authentication token management

### Media & Recording
- **react-mic** - Audio recording capabilities
- **Speech Recognition API** - Voice-to-text conversion
- **Text-to-Speech API** - Audio question playback
- **MediaRecorder API** - Video recording functionality

### Development Tools
- **ESLint** - Code linting with TypeScript support
- **Prettier** - Code formatting
- **Husky** - Git hooks for pre-commit checks
- **lint-staged** - Run linters on staged files

## Getting Started

### Prerequisites

- Node.js (v18 or later recommended)
- npm or yarn package manager
- Modern browser with WebRTC support

### Environment Variables

Create a `.env.local` file in the root directory:

```bash
NEXT_PUBLIC_SUPER_ADMIN_HOST=your_backend_api_url
```

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd project-h-interview-web-app
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Set up Git hooks:

```bash
npm run prepare
```

### Running the Development Server

Start the Next.js development server with Turbopack:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the app.

### Building for Production

To build the app for production:

```bash
npm run build
```

To start the production server:

```bash
npm start
```

### Code Quality

Run linting and formatting:

```bash
# Check for linting issues
npm run lint:check

# Fix linting issues
npm run lint:fix

# Check formatting
npm run format:check

# Format code
npm run format:write
```

## Project Structure

```
src/
├── app/                      # Next.js app directory with pages and API routes
│   ├── api/                  # API route handlers
│   │   ├── candidate/        # Candidate-related endpoints
│   │   │   ├── interviews/   # Interview listings API
│   │   │   ├── resume/       # Resume download API
│   │   │   ├── jd-details/   # Job description API
│   │   │   ├── verify-otp/   # OTP verification API
│   │   │   └── route.ts      # Candidate details API
│   │   └── enums/            # Dropdown enum values API
│   ├── dashboard/            # Candidate dashboard pages and components
│   ├── Interview/            # Interview related pages and WebSocket hooks
│   ├── otpVerification/      # OTP verification page
│   └── page.tsx              # Landing page with email sign-in
├── components/               # Reusable React components
│   ├── InterviewComponents/  # Interview UI components (video, chat, controls)
│   ├── jobsComponent/        # Job listing components
│   ├── emailverification/   # Email verification components
│   ├── Header/               # Header components with navigation
│   ├── Models/               # Data models and UI components
│   ├── OTP-verification-component/ # OTP verification UI
│   ├── constant/             # Constant components (job cards, etc.)
│   └── ui/                   # UI primitives (buttons, inputs, dialogs, etc.)
├── hooks/                    # Custom React hooks
│   └── use-toast.ts          # Toast notification hook
├── lib/                      # Utility functions and API helpers
│   ├── utils.ts              # General utilities
│   ├── apiHelper.ts          # API request helper
│   └── errorHandler.ts       # Error handling utilities
├── redux/                    # Redux slices and store configuration
│   ├── store.ts              # Redux store setup
│   ├── constSlice.tsx        # Constants and user state
│   └── NavSlice.tsx          # Navigation state
├── services/                 # Service layer for API calls
│   └── candidate.service.ts  # Candidate-related API services
├── Models/                   # TypeScript interfaces and types
│   ├── candidate.ts          # Candidate data models
│   ├── job.ts                # Job and interview data models
│   └── enums.ts              # Enum interfaces
├── assets/                   # Static assets (images, animations)
└── public/                   # Public static files
```

## API Endpoints

### Authentication
- `POST /api/candidate/send-otp` - Send OTP to candidate email for login
- `POST /api/candidate/verify-otp` - Verify OTP and set authentication cookie

### Candidate Management
- `POST /api/candidate` - Fetch logged-in candidate user info
- `POST /api/candidate/sign-out` - Sign out candidate and clear session

### Job & Interview Management
- `POST /api/candidate/interviews` - Fetch paginated job interview listings with search
- `POST /api/candidate/resume` - Download candidate resume as blob
- `POST /api/candidate/jd-details` - Fetch detailed job description

### System Data
- `POST /api/enums` - Fetch dropdown enum values for forms

## Key Features Implementation

### Authentication Flow
1. Email input and OTP request
2. OTP verification with JWT token
3. Secure cookie-based session management
4. Automatic token refresh and validation

### Interview System
1. Real-time WebSocket connection for live interviews
2. Audio recording and playback capabilities
3. Video recording with screen sharing requirements
4. Speech-to-text transcription display
5. Interview controls (skip, complete, end)

### State Management
- Redux Toolkit for global state
- Persistent user session data
- Real-time enum values from backend
- Navigation state management

### UI/UX Features
- Responsive design with Tailwind CSS
- Accessible components with Radix UI
- Smooth animations with Framer Motion
- Toast notifications for user feedback
- Loading states and error handling

## Development Guidelines

### Code Style
- TypeScript for type safety
- ESLint configuration with React and Next.js rules
- Prettier for consistent formatting
- Husky pre-commit hooks for quality assurance

### Component Structure
- Functional components with hooks
- Custom hooks for reusable logic
- Service layer for API interactions
- Proper error handling and loading states

### File Naming Conventions
- PascalCase for React components
- camelCase for utilities and hooks
- kebab-case for API routes
- Descriptive file and folder names

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

**Note:** WebRTC features require modern browser support for optimal interview functionality.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow
1. Run `npm run lint:check` before committing
2. Ensure all TypeScript errors are resolved
3. Test interview functionality in supported browsers
4. Update documentation for new features

## Deployment

### Vercel (Recommended)
The app is optimized for deployment on Vercel:

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
For deployment on other platforms, ensure:
- Node.js 18+ runtime
- Environment variables are properly set
- Build command: `npm run build`
- Start command: `npm start`

## Troubleshooting

### Common Issues

**WebSocket Connection Failed:**
- Check backend API URL in environment variables
- Ensure WebSocket endpoint is accessible
- Verify network firewall settings

**Audio/Video Not Working:**
- Grant browser permissions for microphone and camera
- Check browser compatibility for WebRTC
- Ensure HTTPS in production for media access

**Authentication Issues:**
- Clear browser cookies and localStorage
- Verify OTP email delivery
- Check JWT token expiration

## Performance Optimization

- Next.js automatic code splitting
- Image optimization with Next.js Image component
- Lazy loading for interview components
- Redux state normalization
- Efficient re-rendering with React.memo

## Security Features

- HTTP-only cookies for authentication
- CSRF protection with SameSite cookies
- Input validation and sanitization
- Secure API communication
- Screen sharing for interview proctoring

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For questions, issues, or feature requests:
- Open an issue on GitHub
- Contact the development team
- Check the documentation for common solutions

---

**Project H Interview Web App** - Revolutionizing the interview experience with AI-powered technology and seamless user interaction.
