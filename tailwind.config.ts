import type { Config } from 'tailwindcss'
const defaultTheme = require('tailwindcss/defaultTheme')

const colors = require('tailwindcss/colors')
const {
    default: flattenColorPalette,
} = require('tailwindcss/lib/util/flattenColorPalette')

const config = {
    darkMode: ['class'],
    content: [
        './pages/**/*.{ts,tsx}',
        './components/**/*.{ts,tsx}',
        './app/**/*.{ts,tsx}',
        './src/**/*.{ts,tsx}',
    ],
    prefix: '',
    theme: {
        container: {
            center: true,
            padding: '2rem',
            screens: {
                '2xl': '1400px',
            },
        },
        extend: {
            boxShadow: {
                custom_card:
                    '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23) !important',
                light_custom_card:
                    '0 4px 6px rgba(255, 255, 255, 0.5) !important',
                'light-bottom': '0 2px 4px rgba(0, 0, 0, 0.1) !important',
                'dark-bottom': '0 2px 4px rgba(255, 255, 255, 0.4) !important',
                'light-top': '0 -2px 4px rgba(0, 0, 0, 0.1)',
                'dark-top': '0 -2px 4px rgba(255, 255, 255, 0.4)',
            },
            colors: {
                blue10: '#073071',
                blue20: '#151D48',
                blue30: '#EBF6FF',
                blue40: '#1E2A47',
                blue50: '#4050E7',
                blue60: '#EBF5FF',
                blue70: '#0D5BD7',
                blue80: '#052963',
                blue90: '#C2E7FE',
                blue100: '#01337A',
                blue110: '#052658',
                blue120: '#1E3A8A',
                blue130: '#E9EEF6',
                blue140: '#344054',
                blue150: '#4299e1',
                blue160: '#bee3f8',
                blue170: '#699CF9',
                blue180: '#04147c',
                blue190: '#10263D',
                blue200: '#a0a3b2',
                blue210: '#f0f4ff',
                blue220: '#2D3D63',
                blue230: '#3b82f6',

                black10: '#000000',
                black20: '#1E1E1E',
                black30: '#4D4E4B',

                white10: '#FFFFFF',
                white20: '#F8F8FF',
                white30: '#F8FAFD',

                red10: '#eb5757',
                red20: '#9E0E0E',
                red30: '#FEF3F2',
                red40: '#CC191A',

                redOrange: '#EA4336',

                yellow10: '#f4b455',
                yellow20: '#FFF9EC',
                yellow30: '#ffefa1',

                orange10: '#ffa412',
                orange20: '#ed8936',
                orange30: '#fbd38d',
                orange40: '#F08A24',

                gray10: '#2D3748',
                gray20: '#74788d',
                gray30: '#d8d6cf',
                gray40: '#D8D8D8',
                gray50: '#EAEAEC',
                gray60: '#CECECE',
                gray70: '#2A2A2A',
                gray80: '#5E5E5E',
                gray90: '#d2d7dc',
                gray95: '#DFDEDE',
                gray100: '#2d2d2d',
                gray110: '#d0d5dd',
                gray120: '#DBDBDB',
                gray130: '#f0f0f0',
                gray140: '#F2F2F2',
                gray150: '#E8EBF2',
                gray160: '#f5f3f3',
                lightGray: '#E8E8E8',
                midGray: '#717171',
                midGray10: '#7C7C7C',
                midGray20: '#838383',
                midgray30: '#797979',
                darkGray: '#4C4C4C',
                darkGray10: '#666565',
                darkGray20: '#1C1B1F',
                darkgray30: '#333333',
                darkGray40: '#111',
                lightgray10: '#BEBEBE',
                lightGray20: '#d9d9d9',
                lightGray30: '#ababab',
                lightGray40: '#E6E6E6',
                darkgray10: '#5B5B5B',
                darkgray50: '#747573',
                darkgray60: '#4C4F4D',
                bordergray: '#E3E3E3',

                blueGray: '#475467',
                blueGray10: '#474E60',
                blueGray20: '#323f4b',

                purple10: '#261B39',

                amber10: '#d7940d',

                brown10: '#5C4033',

                violet10: '6750A4',

                mediumgreen: '#38a169',
                mediumgreen10: '#4DB152',
                mediumGreen20: '#3dcf81',
                lightGreen: '#c6f6d5',
                green10: '#56cb4e',
                green20: 'aff6f1',
                green30: '#3DAE5D',
                green40: '#25A059',
                goldyellow: '#E18B29',
                border: 'hsl(var(--border))',
                input: 'hsl(var(--input))',
                ring: 'hsl(var(--ring))',
                background: 'hsl(var(--background))',
                foreground: 'hsl(var(--foreground))',
                primary: {
                    DEFAULT: 'hsl(var(--primary))',
                    foreground: 'hsl(var(--primary-foreground))',
                },
                secondary: {
                    DEFAULT: 'hsl(var(--secondary))',
                    foreground: 'hsl(var(--secondary-foreground))',
                },
                destructive: {
                    DEFAULT: 'hsl(var(--destructive))',
                    foreground: 'hsl(var(--destructive-foreground))',
                },
                muted: {
                    DEFAULT: 'hsl(var(--muted))',
                    foreground: 'hsl(var(--muted-foreground))',
                },
                accent: {
                    DEFAULT: 'hsl(var(--accent))',
                    foreground: 'hsl(var(--accent-foreground))',
                },
                popover: {
                    DEFAULT: 'hsl(var(--popover))',
                    foreground: 'hsl(var(--popover-foreground))',
                },
                card: {
                    DEFAULT: 'hsl(var(--card))',
                    foreground: 'hsl(var(--card-foreground))',
                },
                chart: {
                    '1': 'hsl(var(--chart-1))',
                    '2': 'hsl(var(--chart-2))',
                    '3': 'hsl(var(--chart-3))',
                    '4': 'hsl(var(--chart-4))',
                    '5': 'hsl(var(--chart-5))',
                },
            },
            borderRadius: {
                lg: 'var(--radius)',
                md: 'calc(var(--radius) - 2px)',
                sm: 'calc(var(--radius) - 4px)',
            },
            keyframes: {
                'accordion-down': {
                    from: {
                        height: '0',
                    },
                    to: {
                        height: 'var(--radix-accordion-content-height)',
                    },
                },
                'accordion-up': {
                    from: {
                        height: 'var(--radix-accordion-content-height)',
                    },
                    to: {
                        height: '0',
                    },
                },
            },
            animation: {
                'accordion-down': 'accordion-down 0.2s ease-out',
                'accordion-up': 'accordion-up 0.2s ease-out',
            },
            fontFamily: {
                poppins: ['Poppins'],
            },
        },
    },
    plugins: [require('tailwindcss-animate'), addVariablesForColors],
} satisfies Config

function addVariablesForColors({ addBase, theme }: any) {
    let allColors = flattenColorPalette(theme('colors'))
    let newVars = Object.fromEntries(
        Object.entries(allColors).map(([key, val]) => [`--${key}`, val]),
    )

    addBase({
        ':root': newVars,
    })
}
export default config
