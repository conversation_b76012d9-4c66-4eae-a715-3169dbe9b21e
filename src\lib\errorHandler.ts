import type { AxiosError } from 'axios'
import axios from 'axios'
import { toast } from '@/components/ui/use-toast'

/**
 * Handles API errors and returns a user-friendly message.
 * Special handling for 401 unauthorized errors to redirect to login.
 *
 * @param error - The error thrown (can be any type).
 * @param fallbackMessage - A default message to show if no specific message is found.
 * @returns A string message describing the error.
 */
export function handleApiError(
    error: unknown,
    fallbackMessage: string = 'Something went wrong.',
): string {
    console.error('API Error:', error)

    let message: string = fallbackMessage

    // Handle custom unauthorized session expired error
    if (
        error instanceof Error &&
        error.message === 'UNAUTHORIZED_SESSION_EXPIRED'
    ) {
        message = 'Your session has expired. Please log in again.'
        toast({
            title: 'Session Expired',
            description: message,
            variant: 'destructive',
        })

        // Redirect to root path on client side
        if (typeof window !== 'undefined') {
            window.location.href = '/'
        }

        return message
    }

    if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError<any>

        const responseMessage: string | undefined =
            axiosError.response?.data?.message ??
            axiosError.response?.data?.error ??
            axiosError.message

        message = responseMessage || fallbackMessage
    } else if (error instanceof Error) {
        message = error.message
    }
    toast({ title: 'Error', description: message, variant: 'destructive' })

    return message
}
