'use client'

import type React from 'react'

interface UserInfoCardProps {
    name: string
    mobileNo: string
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ name, mobileNo }) => (
    <div className="bg-white border border-gray-200 rounded-md p-4 mb-4">
        <div className="space-y-2">
            <div className="text-sm text-gray-700">
                <span className="font-medium">Name: </span>
                <span>{name}</span>
            </div>
            <div className="text-sm text-gray-700">
                <span className="font-medium">Mobile No: </span>
                <span>{mobileNo}</span>
            </div>
        </div>
    </div>
)

export default UserInfoCard
