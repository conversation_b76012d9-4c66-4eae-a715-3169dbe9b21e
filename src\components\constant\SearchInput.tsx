'use client'

import React from 'react'
import { Search } from 'lucide-react'
import { Input } from '../ui/input'

interface SearchInputProps {
    placeholder?: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    className?: string
    inputClassName?: string
}

export default function SearchInput({
    placeholder = 'Search...',
    value,
    onChange,
    className = '',
    inputClassName = '',
}: SearchInputProps) {
    return (
        <div className={`relative w-full max-w-sm ${className}`}>
            <Input
                type="text"
                placeholder={placeholder}
                value={value}
                onChange={onChange}
                className={`block w-full pl-10 pr-3 py-3 border border-[#d9d9d9] rounded-md bg-white focus:outline-none focus:ring-1 ${inputClassName}`}
            />
            <Search
                size={20}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
            />
        </div>
    )
}
