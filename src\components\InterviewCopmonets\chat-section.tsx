'use client'

import Image from 'next/image'
import { memo, useEffect, useRef } from 'react'

interface ChatSectionProps {
    currentQuestion: string
    transcript: string
    interimTranscript: string
    isListening: boolean
    isSpeaking: boolean
    isUserSpeaking?: boolean
    pendingTranscript?: string
}

const SpeakingIndicator = memo(() => (
    <div className="flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg">
        <div className="flex space-x-1">
            {[0, 1, 2, 3, 4].map((i) => (
                <div
                    key={i}
                    className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"
                    style={{
                        animationDelay: `${i * 0.1}s`,
                        animationDuration: '1s',
                    }}
                />
            ))}
        </div>
    </div>
))

SpeakingIndicator.displayName = 'SpeakingIndicator'

const ChatSection = memo(
    ({
        currentQuestion,
        transcript,
        interimTranscript,
        isListening,
        isSpeaking,
        isUserSpeaking,
        pendingTranscript,
    }: ChatSectionProps) => {
        const transcriptContainerRef = useRef<HTMLDivElement>(null)
        useEffect(() => {
            if (transcriptContainerRef.current) {
                transcriptContainerRef.current.scrollTop =
                    transcriptContainerRef.current.scrollHeight
            }
        }, [transcript, interimTranscript])

        return (
            <div className="flex flex-col h-full">
                <div className="bg-[#F0F2EF] rounded-2xl p-6 flex-1 mb-6">
                    <div className="mb-8">
                        <h2 className="text-lg font-semibold text-gray-900 mb-4">
                            AI Interviewer
                        </h2>
                        <div className="text-gray-700 leading-relaxed text-base">
                            {currentQuestion}
                        </div>
                    </div>
                    <div className="border-l-4 border-gray-300 my-6 ml-4" />
                    <div>
                        <h2 className="text-lg font-semibold text-gray-900 mb-4">
                            YOU
                        </h2>
                        <div
                            ref={transcriptContainerRef}
                            className="text-gray-700 leading-relaxed text-base min-h-[80px] max-h-48 overflow-y-auto"
                        >
                            {transcript}
                            <span className="text-blue-500">
                                {interimTranscript}
                            </span>
                            {pendingTranscript && (
                                <span className="text-orange-500 italic">
                                    {pendingTranscript}
                                </span>
                            )}
                            {!(
                                transcript ||
                                interimTranscript ||
                                pendingTranscript
                            ) && (
                                <span className="text-gray-400 italic">
                                    {isListening
                                        ? 'Listening...'
                                        : isSpeaking
                                          ? 'AI is speaking...'
                                          : 'Waiting for your response...'}
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                {/* AI Interviewer Avatar with Speaking Indicator */}
                <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div>
                                <h3 className="font-semibold text-gray-800 text-lg">
                                    AI Interviewer
                                </h3>
                                <p className="text-sm text-gray-600">
                                    {isSpeaking
                                        ? 'Speaking...'
                                        : isListening
                                          ? 'Listening to you'
                                          : 'Ready'}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    },
)

ChatSection.displayName = 'ChatSection'

export default ChatSection
