// hooks/use-media-recorder.ts
import { useState, useRef, useCallback, useEffect } from 'react'
import type { Root, Type } from 'protobufjs'
import { load } from 'protobufjs'

export const useMediaRecorder = () => {
    const mediaRecorderRef = useRef<MediaRecorder | null>(null)
    const internalRecordedChunksRef = useRef<Blob[]>([]) // For the full recording
    const streamRef = useRef<MediaStream | null>(null)
    const segmentTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    const [isRecording, setIsRecording] = useState(false)
    const [videoChunkType, setVideoChunkType] = useState<Type | null>(null)

    const onSegmentCompleteRef = useRef<((segment: Blob) => void) | null>(null)

    // Load protobuf VideoChunk type once
    useEffect(() => {
        load('/video.proto', (err, root?: Root) => {
            if (err) {
                console.error('Failed to load proto:', err)
                return
            }
            const VideoChunk = root?.lookupType('VideoChunk')
            setVideoChunkType(VideoChunk as Type)
        })
    }, [])

    const createMediaRecorder = useCallback((stream: MediaStream) => {
        const VIDEO_BITRATE = 800_000
        const AUDIO_BITRATE = 64_000
        const MimeTypeVP9 = 'video/webm; codecs=vp9'
        const MimeTypeVP8 = 'video/webm; codecs=vp8'
        const MimeTypeGenericWebm = 'video/webm'

        let selectedMimeType = MimeTypeGenericWebm

        if (MediaRecorder.isTypeSupported(MimeTypeVP9)) {
            selectedMimeType = MimeTypeVP9
        } else if (MediaRecorder.isTypeSupported(MimeTypeVP8)) {
            selectedMimeType = MimeTypeVP8
        }

        try {
            return new MediaRecorder(stream, {
                mimeType: selectedMimeType,
                videoBitsPerSecond: VIDEO_BITRATE,
                audioBitsPerSecond: AUDIO_BITRATE,
            })
        } catch (e) {
            console.error(
                'Failed to create MediaRecorder with specified options:',
                e,
            )
            return new MediaRecorder(stream, {
                mimeType: MimeTypeGenericWebm,
            })
        }
    }, [])

    const startNewSegment = useCallback(() => {
        if (!streamRef.current) return

        // Create new recorder
        const recorder = createMediaRecorder(streamRef.current)
        mediaRecorderRef.current = recorder

        // Set up data handler
        recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                internalRecordedChunksRef.current.push(event.data)
            }
        }

        // Set up stop handler
        recorder.onstop = () => {
            if (recorder.state === 'inactive' && onSegmentCompleteRef.current) {
                const segmentBlob = new Blob(
                    internalRecordedChunksRef.current,
                    {
                        type: recorder.mimeType,
                    },
                )
                onSegmentCompleteRef.current(segmentBlob)
                internalRecordedChunksRef.current = []
            }
        }

        // Start recording
        recorder.start()

        // Schedule next segment
        segmentTimeoutRef.current = setTimeout(() => {
            if (recorder.state === 'recording') {
                recorder.stop()
                startNewSegment()
            }
        }, 5000)
    }, [createMediaRecorder])

    // New: send chunk as protobuf over websocket
    const sendVideoChunk = useCallback(
        async (
            segment: Blob,
            ws: WebSocket,
            speaker: number,
            questionIndex: number,
        ) => {
            if (!videoChunkType) return
            const db = new Uint8Array(await segment.arrayBuffer())
            const message = videoChunkType.create({
                speaker,
                timestamp: Date.now(),
                questionIndex,
                videoData: db,
            })
            const buffer = videoChunkType.encode(message).finish()
            console.log(
                'Preparing to send video chunk. ws:',
                ws,
                'readyState:',
                ws?.readyState,
            )
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(buffer)
            }
        },
        [videoChunkType],
    )

    const startRecording = useCallback(
        (stream: MediaStream, onSegmentComplete: (segment: Blob) => void) => {
            if (!stream) {
                console.error('No stream provided for recording.')
                return
            }

            // Store stream and callback
            streamRef.current = stream
            onSegmentCompleteRef.current = onSegmentComplete
            internalRecordedChunksRef.current = []

            // Start first segment
            startNewSegment()
            setIsRecording(true)
        },
        [startNewSegment],
    )

    // New: start recording with websocket, speaker, questionIndex, and 5 min interval
    const startRecordingWithWebSocket = useCallback(
        (
            stream: MediaStream,
            ws: WebSocket,
            speaker: number,
            questionIndex: number,
        ) => {
            console.log(
                'startRecordingWithWebSocket called',
                ws,
                speaker,
                questionIndex,
            )
            if (!stream) {
                return
            }
            streamRef.current = stream
            internalRecordedChunksRef.current = []
            setIsRecording(true)

            // Start first segment
            const recordSegment = () => {
                if (!streamRef.current) return
                const recorder = createMediaRecorder(streamRef.current)
                mediaRecorderRef.current = recorder
                internalRecordedChunksRef.current = []

                recorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        internalRecordedChunksRef.current.push(event.data)
                    }
                }
                recorder.onstop = () => {
                    if (recorder.state === 'inactive') {
                        const segmentBlob = new Blob(
                            internalRecordedChunksRef.current,
                            { type: recorder.mimeType },
                        )
                        sendVideoChunk(segmentBlob, ws, speaker, questionIndex)
                        internalRecordedChunksRef.current = []
                    }
                }
                recorder.start()

                segmentTimeoutRef.current = setTimeout(() => {
                    if (recorder.state === 'recording') {
                        recorder.stop()
                        recordSegment()
                    }
                }, 5000) // 5 seconds for debugging
            }
            recordSegment()
        },
        [createMediaRecorder, sendVideoChunk],
    )

    const stopRecording = useCallback(() => {
        if (segmentTimeoutRef.current) {
            clearTimeout(segmentTimeoutRef.current)
            segmentTimeoutRef.current = null
        }

        if (mediaRecorderRef.current?.state === 'recording') {
            mediaRecorderRef.current.stop()
        }

        setIsRecording(false)
    }, [])

    const downloadRecording = useCallback(() => {
        if (internalRecordedChunksRef.current.length > 0) {
            const blob = new Blob(internalRecordedChunksRef.current, {
                type: mediaRecorderRef.current?.mimeType || 'video/webm',
            })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'interview_full_recording.webm'
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
        }
    }, [])

    return {
        startRecording,
        stopRecording,
        downloadRecording,
        isRecording,
        startRecordingWithWebSocket,
    }
}
