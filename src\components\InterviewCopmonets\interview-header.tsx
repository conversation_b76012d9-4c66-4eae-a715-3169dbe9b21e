'use client'

import { memo } from 'react'

interface InterviewHeaderProps {
    remainingTime: string
    timeProgress: number
}

const InterviewHeader = memo(
    ({ remainingTime, timeProgress }: InterviewHeaderProps) => (
        <header className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
                <div className="text-blue-600">
                    <svg
                        width="28"
                        height="28"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M12 2L2 7L12 12L22 7L12 2Z"
                            fill="currentColor"
                        />
                        <path
                            d="M2 17L12 22L22 17"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                        <path
                            d="M2 12L12 17L22 12"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">
                    AI Interview
                </h1>
            </div>
            <div className="flex items-center gap-6">
                <div className="text-right">
                    <div className="text-sm text-gray-500 mb-1">
                        Remaining time
                    </div>
                    <div className="w-48 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                            className="h-full bg-green-500 transition-all duration-300 ease-out"
                            style={{ width: `${timeProgress}%` }}
                        />
                    </div>
                </div>
                <div className="text-2xl font-bold text-gray-900 min-w-[80px]">
                    {remainingTime}
                </div>
            </div>
        </header>
    ),
)

InterviewHeader.displayName = 'InterviewHeader'

export default InterviewHeader
