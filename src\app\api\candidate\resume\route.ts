import { api<PERSON><PERSON><PERSON> } from '@/lib/apiHelper'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import * as fileType from 'file-type'

export async function POST(request: NextRequest) {
    try {
        const userData = await request.json()
        if (!userData) {
            return new NextResponse('Resume ID is required', { status: 400 })
        }

        const fileBuffer = await apiHelper(
            `api/candidate/resume/download/${userData.resumeId}`,
            {
                method: 'GET',
                responseType: 'arraybuffer',
            },
        )

        if (!fileBuffer || !(fileBuffer instanceof Buffer)) {
            return new NextResponse('Invalid file data received.', {
                status: 500,
            })
        }

        const type = await fileType.fileTypeFromBuffer(fileBuffer)
        const contentType = type?.mime || 'application/octet-stream'

        return new NextResponse(fileBuffer, {
            status: 200,
            headers: {
                'Content-Type': contentType,
                'Content-Disposition': 'inline; filename="resume"',
            },
        })
    } catch (error: any) {
        let message = 'Failed to fetch resume.'
        const errorData = error?.response?.data

        if (Buffer.isBuffer(errorData)) {
            try {
                message = errorData.toString('utf-8')
            } catch {
                message = 'Error decoding backend error response.'
            }
        } else if (typeof errorData === 'string') {
            message = errorData
        } else if (errorData?.message) {
            message = errorData.message
        } else if (error?.message) {
            message = error.message
        }

        return new NextResponse(
            JSON.stringify({
                error: message,
                message: 'Failed to fetch resume.',
                success: false,
            }),
            {
                status: error?.response?.status || 500,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    }
}
