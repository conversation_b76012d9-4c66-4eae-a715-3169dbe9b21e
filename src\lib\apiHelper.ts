import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'
import { cookies, headers } from 'next/headers'
import { redirect, RedirectType } from 'next/navigation' // If you want to handle redirects directly in Next.js
import { verifyToken } from './verifyToken'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

interface ApiRequestOptions {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    body?:
        | Record<string, string | number | boolean | null | undefined>
        | FormData
    headers?: Record<string, string>
    responseType?: AxiosRequestConfig['responseType']
    request?: NextRequest
}

const API_URL = process.env.NEXT_PUBLIC_SUPER_ADMIN_HOST

export const apiHelper = async (
    url: string,
    options: ApiRequestOptions = {},
) => {
    // Force output to terminal immediately
    process.stdout.write(`\n🔥 [API HELPER] FUNCTION CALLED - URL: ${url}\n`)
    process.stdout.write(`🔥 [API HELPER] METHOD: ${options.method || 'GET'}\n`)
    console.log('🔥 [API HELPER] STARTING EXECUTION')

    try {
        const token = (await cookies()).get('Interview_toke')?.value
        const refreshToken = (await cookies()).get('refreshToken')?.value // Get refreshToken from cookies

        if (!token && refreshToken) {
            const result = await verifyToken(refreshToken)

            if (
                result.valid &&
                result.refreshed &&
                result.headers?.['Set-Cookie']
            ) {
                const response = NextResponse.next()
                response.headers.set('Set-Cookie', result.headers['Set-Cookie'])
                return response
            }
        }

        // if (!token) throw new Error('Authorization token is missing.')
        // try {
        //     const payload = JSON.parse(atob(token.split('.')[1]))
        //     if (payload.exp < Math.floor(Date.now() / 1000)) {
        //         throw new Error('Token has expired.')
        //     }
        // } catch {
        //     const redirectUrl = new URL(
        //         '/',
        //         options.request?.url ?? 'http://localhost:3000',
        //     )

        //     const response = NextResponse.redirect(redirectUrl.toString(), 307)

        //     response.cookies.set('Interview_toke', '', {
        //         path: '/',
        //         expires: new Date(0),
        //     })
        //     response.cookies.set('refreshToken', '', {
        //         path: '/',
        //         expires: new Date(0),
        //     })

        //     return response
        // }

        // Construct query string for GET requests
        let queryString = ''
        if (
            options.method === 'GET' &&
            options.body &&
            !(options.body instanceof FormData) &&
            typeof options.body === 'object' &&
            !Array.isArray(options.body)
        ) {
            queryString = `?${new URLSearchParams(options.body as Record<string, string>).toString()}`
        }

        // Determine content type based on body type
        const isFormData = options.body instanceof FormData
        const contentType = isFormData
            ? 'multipart/form-data'
            : 'application/json'

        // Axios request configuration
        const config: AxiosRequestConfig = {
            method: options.method || 'GET',
            url: `${API_URL}/${url}${queryString}`,
            headers: {
                'Content-Type': contentType,
                Authorization: `Bearer ${token}`,
                ...options.headers,
            },
            data: options.method !== 'GET' ? options.body : undefined,
            responseType: options.responseType || 'json',
        }

        // Make the request
        console.log(
            '🚀 [API HELPER] Making request to:',
            `${API_URL}/${url}${queryString}`,
        )
        console.log('🚀 [API HELPER] Request method:', options.method || 'GET')

        const response = await axios(config)

        console.log('✅ [API HELPER] Response status:', response.status)
        console.log('✅ [API HELPER] Response data:', response.data)

        if (options.responseType === 'arraybuffer') {
            return Buffer.from(response.data) // ensure it's treated as Buffer
        }
        return response.data
    } catch (error: unknown) {
        console.log('❌ [API HELPER] Error occurred:', error)
        if (axios.isAxiosError(error)) {
            console.log('❌ [API HELPER] Axios error response:', error.response)
            if (error.response) {
                // Handle 401 Unauthorized - token expired or invalid
                // Exclude login, logout, and sign-out endpoints from strict 401 handling
                if (
                    error.response.status === 401 &&
                    !url.includes('login') &&
                    !url.includes('logout') &&
                    !url.includes('sign-out')
                ) {
                    try {
                        const cookieStore = await cookies()
                        // Remove both tokens with consistent naming
                        cookieStore.delete('Interview_toke')
                        cookieStore.delete('refreshToken')

                        console.log(
                            '401 Unauthorized: Tokens removed, redirecting to login',
                        )

                        // For server-side, throw a custom error that can be caught by the calling component
                        // The component should handle the redirect on the client side
                        const unauthorizedError = new Error(
                            'UNAUTHORIZED_SESSION_EXPIRED',
                        )
                        ;(unauthorizedError as any).status = 401
                        throw unauthorizedError
                    } catch (cookieError) {
                        console.error('Error removing cookies:', cookieError)
                        // Still throw the unauthorized error even if cookie removal fails
                        const unauthorizedError = new Error(
                            'UNAUTHORIZED_SESSION_EXPIRED',
                        )
                        ;(unauthorizedError as any).status = 401
                        throw unauthorizedError
                    }
                }

                if (
                    error.response.data &&
                    typeof error.response.data === 'object' &&
                    'message' in error.response.data
                ) {
                    console.error(`API Error: ${error.response.data.message}`)
                } else {
                    console.error(`API Error: Status ${error.response.status}`)
                }
            } else if (error.request) {
                // The request was made but no response was received
                console.error(
                    'API Request Error: No response received from server.',
                )
                console.error('API Request Error details:', error.request) // Log full request for debugging
            } else {
                // Something happened in setting up the request that triggered an Error
                console.error(`Error in API request setup: ${error.message}`)
            }
        } else if (error instanceof Error) {
            // This error is a standard JavaScript Error (e.g., from your token logic)
            console.error(`General Error: ${error.message}`)
        } else {
            // This error is something entirely unexpected
            console.error('An unexpected error occurred.')
            console.error('Unexpected error details:', error) // Log the unknown error for debugging
        }

        throw error // Always rethrow if you want calling functions to also handle it
    }
}
