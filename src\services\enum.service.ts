import { useCallback, useMemo } from 'react'
import axios from 'axios'
import { toast } from 'sonner'

export const useEnumService = () => {
    const fetchEnums = useCallback(async (): Promise<{
        success: boolean
        data?: any
        message?: string
    }> => {
        try {
            const response = await axios.post('/api/enums')

            if (response.status === 200) {
                return {
                    success: true,
                    data: response.data.data, // adjust this line if needed
                }
            }

            return { success: false, message: 'Failed to fetch enum values' }
        } catch (error: any) {
            const message =
                error?.response?.data?.error ||
                error?.response?.data?.message ||
                'Error while fetching enums.'

            toast.error(message)
            return { success: false, message }
        }
    }, [])

    return useMemo(() => ({ fetchEnums }), [fetchEnums])
}
