interface JobSkill {
    skillName: string
    skillLevel: 'ADVANCED' | 'INTERMEDIATE' | 'BEGINNER'
    skillType: 'MUST_TO_HAVE' | 'GOOD_TO_HAVE' | 'NICE_TO_HAVE'
}

interface JobEducationInfo {
    jobEducationInfoId: string
    highestQualification: string
    percentage: string
    jobCertifications: JobCertification[]
}
interface JobCertification {
    certificationsId: string
    certificationsName: string
    skillType: 'MUST_TO_HAVE' | 'GOOD_TO_HAVE'
    jobEducationInfo: string
}
export interface JDData {
    detailedJobDescription: any
    id: string
    interviewCount: number
    jobTemplateId?: string
    jobDescriptionId: string
    title: string
    experienceMin: number
    experienceMax: number
    annualSalaryMin: number
    annualSalaryMax: number
    interviewType: string
    workMode: string
    jobLocation: string
    noticePeriod: string
    keywordSearch: string[]
    skills: JobSkill[]
    educationInfo: JobEducationInfo
    jobDescription: string
    interviewInstructionsAndQuestions: string
    userId: string
    userName: string
    totalApplicants: number
    interviewsTaken: number
    active: boolean
    interviewRatio: number
    shortlistedCandidate: number
    expiryToDateAndTime?: string
    expiryFromDateAndTime?: string
    publishToMobileApp?: boolean
    autoRejectionScore?: string
}
