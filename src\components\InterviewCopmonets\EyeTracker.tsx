import React, { useEffect, useRef, useState } from 'react'
import clm from 'clmtrackr'

const EyeTracker: React.FC = () => {
    const videoRef = useRef<HTMLVideoElement | null>(null)
    const trackerRef = useRef<any>(null)
    const [lookAwayCount, setLookAwayCount] = useState<number>(0)
    const isLookingAwayRef = useRef<boolean>(false) // track current state
    useEffect(() => {
        const video = videoRef.current
        if (!video) return

        navigator.mediaDevices.getUserMedia({ video: true }).then((stream) => {
            video.srcObject = stream
            video.play()

            const tracker = new clm.tracker()
            tracker.init()
            tracker.start(video)
            trackerRef.current = tracker

            const checkLook = () => {
                const positions = tracker.getCurrentPosition()
                if (positions) {
                    const noseX = positions[62][0]
                    const faceCenterX = (positions[13][0] + positions[1][0]) / 2
                    const deviation = noseX - faceCenterX

                    const threshold = 15
                    const isLookingAway = Math.abs(deviation) > threshold

                    if (isLookingAway && !isLookingAwayRef.current) {
                        // Just started looking away
                        setLookAwayCount((count) => count + 1)
                        isLookingAwayRef.current = true
                    } else if (!isLookingAway) {
                        // Looking straight again
                        isLookingAwayRef.current = false
                    }
                }
                requestAnimationFrame(checkLook)
            }

            checkLook()
        })
    }, [])

    return (
        <div style={{ textAlign: 'center' }}>
            <h2>Eye Tracker</h2>
            <video ref={videoRef} width={400} height={300} />
            <h3>Looked sideways count: {lookAwayCount}</h3>
        </div>
    )
}

export default EyeTracker
