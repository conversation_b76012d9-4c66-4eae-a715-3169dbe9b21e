'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    BookOpen,
    AlertTriangle,
    Mic,
    Camera,
    ArrowRight,
    CheckCircle2,
} from 'lucide-react'
import CameraTest from './camera-test'
import MicrophoneTest from './microphone-test'
import { useRouter, useSearchParams } from 'next/navigation'

export default function DeviceTestingPage() {
    const [microphoneTested, setMicrophoneTested] = useState(false)
    const [cameraTested, setCameraTested] = useState(false)
    const [showMicrophoneTest, setShowMicrophoneTest] = useState(false)
    const [showCameraTest, setShowCameraTest] = useState(false)
    const [allTested, setAllTested] = useState(false)
    const router = useRouter()
    const searchParams = useSearchParams()
    const title = searchParams.get('title') || 'Unknown Title'
    const company = searchParams.get('company') || 'Unknown Company'
    const location = searchParams.get('location') || 'Unknown Location'
    const interviewId = searchParams.get('interviewId')
    useEffect(() => {
        if (microphoneTested && cameraTested) {
            setAllTested(true)
        }
    }, [microphoneTested, cameraTested])

    return (
        <div className="container max-w-4xl mx-auto py-8 px-4">
            <div className="space-y-2 mb-8">
                <h1 className="text-2xl font-bold">{title}</h1>
                <p className="text-muted-foreground">
                    {company} - {location}
                </p>
            </div>

            <Card className="mb-8">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <BookOpen className="h-5 w-5 text-amber-500" />
                        Instructions
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>Environment:</strong> Choose a quiet,
                            well-lit place with a stable internet connection.
                        </p>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>Device:</strong> Use a laptop or desktop
                            with an updated browser (Chrome recommended)
                        </p>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>No External Help:</strong> Do not use mobile
                            phones, chat tools, or other coding aids during the
                            test.
                        </p>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>Full-Screen Mode:</strong> The interview
                            will run in full-screen mode. Switching tabs may
                            lead to auto-submission.
                        </p>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>Language:</strong> All answers must be in
                            English.
                        </p>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>Time Management:</strong> Each section is
                            timed. Use your time wisely and avoid lingering too
                            long on one question
                        </p>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="text-primary mt-0.5">▶</div>
                        <p>
                            <strong>Final Submission:</strong> Answers are
                            auto-saved, but click "Submit" once you finish.
                        </p>
                    </div>

                    <div className="flex items-start gap-3 text-amber-600">
                        <AlertTriangle className="h-5 w-5 mt-0.5" />
                        <p>
                            <strong>
                                Plagiarism or suspicious activity will result in
                                disqualification.
                            </strong>
                        </p>
                    </div>
                </CardContent>
            </Card>

            <Card className="mb-8">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <div className="flex items-center justify-center w-6 h-6 border border-muted-foreground rounded">
                            {microphoneTested && cameraTested ? (
                                <CheckCircle2 className="text-green-600 w-5 h-5" />
                            ) : (
                                <div className="w-3 h-3 bg-muted-foreground" />
                            )}
                        </div>
                        Check Device
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="mb-6">
                        Please test your camera and microphone to ensure they
                        are working properly
                    </p>

                    <div className="flex flex-wrap gap-4">
                        {microphoneTested ? (
                            <div className="flex items-center text-green-600 font-semibold gap-2">
                                <CheckCircle2 className="w-5 h-5" /> Microphone
                                tested successfully!
                            </div>
                        ) : (
                            <Button
                                variant="outline"
                                className="flex items-center gap-2"
                                onClick={() => setShowMicrophoneTest(true)}
                            >
                                <Mic className="h-4 w-4" />
                                Test Microphone
                            </Button>
                        )}

                        {cameraTested ? (
                            <div className="flex items-center text-green-600 font-semibold gap-2">
                                <CheckCircle2 className="w-5 h-5" /> Camera
                                tested successfully!
                            </div>
                        ) : (
                            <Button
                                variant="outline"
                                className="flex items-center gap-2"
                                onClick={() => setShowCameraTest(true)}
                            >
                                <Camera className="h-4 w-4" />
                                Test Camera
                            </Button>
                        )}
                    </div>
                    {allTested && (
                        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                            <div className="bg-white p-8 rounded shadow-lg flex flex-col items-center min-w-[350px]">
                                <CheckCircle2 className="h-16 w-16 text-green-600 mb-4" />
                                <div className="text-lg font-semibold mb-2">
                                    Camera & Microphone tested successfully
                                </div>
                                <div className="text-center mb-4">
                                    Your interview will be recorded.
                                </div>
                                <Button
                                    className="px-6 py-2 bg-blue-900 text-white rounded"
                                    onClick={() => setAllTested(false)}
                                >
                                    OK
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            <div className="flex justify-center">
                <Button
                    size="lg"
                    className="px-8 bg-blue-800 hover:bg-blue-900"
                    onClick={() => {
                        router.push(`/Interview?interviewId=${interviewId}`)
                    }}
                    disabled={!microphoneTested || !cameraTested}
                >
                    Start Interview
                    <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
            </div>

            {/* Test modals */}
            {showMicrophoneTest && (
                <MicrophoneTest
                    onSuccess={() => {
                        setMicrophoneTested(true)
                        setShowMicrophoneTest(false)
                    }}
                />
            )}

            {showCameraTest && (
                <CameraTest
                    onSuccess={() => {
                        setCameraTested(true)
                        setShowCameraTest(false)
                    }}
                />
            )}
        </div>
    )
}
