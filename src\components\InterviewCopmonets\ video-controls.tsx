'use client'

import { memo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { PhoneOff } from 'lucide-react'

interface VideoControlsProps {
    onEndInterview: () => void
    onSkip: () => void
    onDone: () => void
    isListening: boolean
    isSpeaking: boolean
}

const VideoControls = memo(
    ({
        onEndInterview,
        onSkip,
        onDone,
        isListening,
        isSpeaking,
    }: VideoControlsProps) => (
        <div className="flex items-center justify-between mt-6 px-4">
            <div className="flex justify-center flex-1">
                <button
                    onClick={onEndInterview}
                    className="flex items-center justify-center w-14 h-14 rounded-full bg-red-500 hover:bg-red-600 text-white transition-colors shadow-lg"
                    aria-label="End interview"
                >
                    <PhoneOff className="h-6 w-6" />
                </button>
            </div>
            <div className="flex gap-3">
                <Button
                    variant="outline"
                    className="px-6 py-2 h-10 border-gray-300 hover:bg-gray-50"
                    onClick={onSkip}
                    disabled={isSpeaking}
                >
                    Skip
                </Button>
                <Button
                    className="px-6 py-2 h-10 bg-blue-600 hover:bg-blue-700 text-white"
                    onClick={onDone}
                    disabled={isSpeaking}
                >
                    Done
                </Button>
            </div>
        </div>
    ),
)

VideoControls.displayName = 'VideoControls'

export default VideoControls
