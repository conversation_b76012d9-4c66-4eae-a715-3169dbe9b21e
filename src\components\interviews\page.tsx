'use client'

import React, { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import InfiniteScroll from 'react-infinite-scroll-component'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import <PERSON><PERSON> from 'lottie-react'
import { toast } from 'sonner'

import JobsData from '../constant/jobs-data'
import { Skeleton } from '@/components/ui/skeleton'
import noDataAvailableAnimation from '@/assets/nodataAvailable.json'
import { useCandidateService } from '@/services/candidate.service'
import { handleApiError } from '@/lib/errorHandler'
import SearchInput from '../constant/SearchInput'
import { useDebounce } from '@/lib/useDebounce'

const size = 8

export default function InterviewListPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const { fetchCandidateInterviews } = useCandidateService()

    const [loading, setLoading] = useState(false)
    const [isInitialLoading, setIsInitialLoading] = useState(true)
    const [page, setPage] = useState(0)
    const [jdData, setJdData] = useState<any[]>([])
    const [hasMore, setHasMore] = useState(true)
    const [totalRecords, setTotalRecords] = useState(0)

    const initialSearch = searchParams.get('search') || ''
    const [searchVal, setSearchVal] = useState(initialSearch)
    const debouncedSearch = useDebounce(searchVal, 500)

    const updateSearchQueryParams = (search: string) => {
        const url = new URL(window.location.href)
        if (!search) url.searchParams.delete('search')
        else url.searchParams.set('search', search)
        window.history.pushState({}, '', url.toString())
    }

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const val = e.target.value
        setSearchVal(val)
        updateSearchQueryParams(val)
    }

    // 🔁 Initial data load when search changes (debounced)
    useEffect(() => {
        const fetchInitialData = async () => {
            setJdData([])
            setPage(0)
            setHasMore(true)
            setIsInitialLoading(true)

            try {
                const { jobs, total } = await fetchCandidateInterviews(
                    0,
                    size,
                    debouncedSearch,
                )
                setJdData(jobs)
                setTotalRecords(total)
                setPage(1)
                setHasMore(jobs.length === size)
            } catch (error) {
                toast.error(handleApiError(error))
                setHasMore(false)
            } finally {
                setIsInitialLoading(false)
            }
        }

        fetchInitialData()
    }, [debouncedSearch, fetchCandidateInterviews])

    // 📥 Pagination scroll fetch
    const fetchData = async () => {
        if (!hasMore) return
        setLoading(true)

        try {
            const { jobs } = await fetchCandidateInterviews(
                page,
                size,
                debouncedSearch,
            )
            setJdData((prev) => [...prev, ...jobs])
            setPage((prev) => prev + 1)
            setHasMore(jobs.length === size)
        } catch (error) {
            toast.error(handleApiError(error))
            setHasMore(false)
        } finally {
            setLoading(false)
        }
    }

    const handleInterviewTake = ({ id, title, company, location }: any) => {
        const query = new URLSearchParams({
            interviewId: id,
            title,
            company,
            location,
        }).toString()
        router.push(`/Interview/device-testing?${query}`)
    }

    return (
        <>
            <div className="flex items-center justify-between w-full mb-4">
                <SearchInput
                    placeholder="Search by Job Title"
                    value={searchVal}
                    onChange={handleSearch}
                    className="ml-3"
                />
                <div className="text-sm text-gray-600 text-right whitespace-nowrap font-medium text-[20px]">
                    {`${jdData.length} of ${totalRecords} Entries`}
                </div>
            </div>

            <div
                id="scrollableDiv"
                className="overflow-y-auto h-full max-h-[400px]"
            >
                {isInitialLoading &&
                    jdData.length === 0 &&
                    Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="p-3">
                            <Skeleton className="h-[150px] w-full rounded-xl" />
                        </div>
                    ))}

                {!isInitialLoading && jdData.length === 0 && (
                    <div className="flex flex-col items-center justify-center py-10 text-gray-500 font-medium">
                        <p className="mt-[-20px]">No jobs found</p>
                        <Lottie
                            animationData={noDataAvailableAnimation}
                            loop
                            autoplay
                            style={{
                                width: 400,
                                height: 400,
                                marginTop: '-30px',
                            }}
                        />
                    </div>
                )}

                {jdData.length > 0 && (
                    <InfiniteScroll
                        dataLength={jdData.length}
                        next={fetchData}
                        hasMore={hasMore}
                        loader={
                            <div className="flex justify-center items-center p-4">
                                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                                <span className="ml-2">
                                    Loading more jobs...
                                </span>
                            </div>
                        }
                        endMessage={
                            <p className="text-center py-5 text-gray-500">
                                <b>Yay! You have seen it all</b>
                            </p>
                        }
                        scrollableTarget="scrollableDiv"
                    >
                        {jdData.map((job: any, index: number) => (
                            <motion.div
                                key={job.id || `job-${index}`}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{
                                    duration: 0.3,
                                    delay: 0.05 * index,
                                }}
                                className="p-3"
                            >
                                <JobsData
                                    job={job}
                                    onTakeInterview={handleInterviewTake}
                                />
                            </motion.div>
                        ))}
                    </InfiniteScroll>
                )}
            </div>
        </>
    )
}
