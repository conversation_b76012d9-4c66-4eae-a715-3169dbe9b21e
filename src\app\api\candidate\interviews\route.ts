import { NextResponse } from 'next/server'
import { api<PERSON>elper } from '@/lib/apiHelper'

export async function POST(req: Request) {
    try {
        const body = await req.json()
        const apiResponse = await api<PERSON>elper(`api/candidate/interviews`, {
            method: 'GET',
            body,
        })

        return new NextResponse(
            JSON.stringify({
                message: 'Interviews fetched successfully!',
                data: apiResponse,
                success: true,
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    } catch (error: any) {
        console.log('🚨 [API ROUTE] Error caught:', error)

        // Handle custom UNAUTHORIZED_SESSION_EXPIRED error
        if (
            error instanceof Error &&
            error.message === 'UNAUTHORIZED_SESSION_EXPIRED'
        ) {
            return new NextResponse(
                JSON.stringify({
                    error: 'Session expired',
                    message: 'Your session has expired. Please log in again.',
                    success: false,
                }),
                {
                    status: 401,
                    headers: { 'Content-Type': 'application/json' },
                },
            )
        }

        const backendMessage =
            error?.response?.data ||
            error?.response?.data?.message ||
            error?.response?.data ||
            error.message ||
            'Failed to fetch interviews.'

        return new NextResponse(
            JSON.stringify({
                error: backendMessage,
                message: 'Failed to fetch interviews.',
                success: false,
            }),
            {
                status:
                    error?.response?.status || (error as any)?.status || 500,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    }
}
