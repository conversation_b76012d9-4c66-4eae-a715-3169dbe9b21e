'use client'
import Header from '@/components/Header/page'
import React from 'react'

interface DashboardLayoutProps {
    children: React.ReactNode
    className?: any
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
    children,
    className,
}: DashboardLayoutProps) => (
    <div className="overflow-none border">
        <Header />
        <div
            className="flex flex-1 mt-[92px] overflow-y-hidden"
            style={{
                height: 'calc(100vh - 64px)',
            }}
        >
            <main
                className=" flex-1 overflow-auto pt-2 pb-3 px-1"
                style={{
                    maxHeight: 'calc(100vh - 64px)',
                }}
            >
                <div
                    className="pl-1 pr-2 mt-0 z-0"
                    style={{
                        padding: '0rem 1.5rem 1.5rem 1.5rem',
                    }}
                >
                    {children}
                </div>
            </main>
        </div>
    </div>
)
export default DashboardLayout
