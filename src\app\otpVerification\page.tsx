'use client'
import { useState } from 'react'
import ResetPasswordComponent from '@/components/emilaverification/page'
import InitialLayout from '../initial-layout'
import OTPVerification from '@/components/OTP-verification-component/page'

export default function ResetPassword() {
    const [otpVerify, setOtpVerify] = useState(false)

    return (
        <InitialLayout>
            <div className="relative w-full h-full flex items-center justify-center ">
                <div
                    key={otpVerify ? 'reset-password' : 'login'}
                    className="relative w-full max-w-md"
                >
                    {otpVerify ? (
                        <ResetPasswordComponent
                            onBack={() => setOtpVerify(false)}
                        />
                    ) : (
                        <OTPVerification onBack={() => setOtpVerify(true)} />
                    )}
                </div>
            </div>
        </InitialLayout>
    )
}
