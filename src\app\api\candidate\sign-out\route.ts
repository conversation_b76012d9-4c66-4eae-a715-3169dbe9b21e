import { api<PERSON>elper } from '@/lib/apiHelper'
import { NextResponse } from 'next/server'

export async function POST() {
    // Always clear cookies first, regardless of backend response
    const response = NextResponse.json({
        message: 'Logged out successfully',
        success: true,
    })

    response.headers.append(
        'Set-<PERSON>ie',
        'Interview_toke=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly',
    )
    response.headers.append(
        'Set-Cookie',
        'refreshToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly',
    )

    try {
        // Optional: Try to call backend logout endpoint
        // If it fails (e.g., token expired), we still consider logout successful
        await api<PERSON><PERSON>per('candidate/logout', {
            method: 'POST',
        })
        console.log('✅ Backend logout successful')
    } catch (error) {
        // Log the error but don't fail the logout process
        console.log(
            '⚠️ Backend logout failed (likely expired token), but local logout successful:',
            error,
        )
        // This is expected behavior when token is expired - the goal is achieved
    }

    return response
}
