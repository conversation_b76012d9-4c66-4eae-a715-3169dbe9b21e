'use client'

import { useState, useRef, useEffect } from 'react'
import Webcam from 'react-webcam'
import { CheckCircle2 } from 'lucide-react'

interface CameraTestProps {
    onSuccess: () => void
}

export default function CameraTest({ onSuccess }: CameraTestProps) {
    const [success, setSuccess] = useState(false)
    const webcamRef = useRef<any>(null)

    useEffect(() => {
        let timer: NodeJS.Timeout
        if (!success) {
            timer = setTimeout(() => {
                if (
                    webcamRef.current &&
                    webcamRef.current.video &&
                    webcamRef.current.video.readyState === 4
                ) {
                    setSuccess(true)
                    setTimeout(() => onSuccess(), 1200)
                }
            }, 3000)
        }
        return () => clearTimeout(timer)
    }, [success, onSuccess])

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white p-8 rounded shadow-lg flex flex-col items-center min-w-[350px]">
                {!success ? (
                    <>
                        <h2 className="text-lg font-semibold mb-4 text-center">
                            Camera Test
                        </h2>
                        <Webcam
                            ref={webcamRef}
                            audio={false}
                            className="mb-4 rounded border w-48 h-36 object-cover"
                        />
                        <div className="flex flex-col items-center justify-center mb-2">
                            <span className="w-8 h-8 border-4 border-blue-700 border-t-transparent rounded-full animate-spin mb-2" />
                        </div>
                    </>
                ) : (
                    <div className="flex flex-col items-center justify-center">
                        <CheckCircle2 className="h-16 w-16 text-green-600 mb-4" />
                        <div className="text-lg font-semibold mb-2">
                            Camera tested successfully
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
