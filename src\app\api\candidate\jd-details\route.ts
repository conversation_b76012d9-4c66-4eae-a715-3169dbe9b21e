import { api<PERSON><PERSON><PERSON> } from '@/lib/apiHelper'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
    let userData
    try {
        userData = await request.json()
    } catch (err) {
        console.error('Failed to parse JSON body:', err)
        return new NextResponse(
            JSON.stringify({
                error: 'Invalid JSON body',
                message: 'Could not parse request body',
                success: false,
            }),
            {
                status: 400,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    }

    try {
        const apiResponse = await apiHelper(
            `api/candidate/jd/getByResumeId/${userData.resumeId}`,
            {
                method: 'GET',
                request,
            },
        )

        return new NextResponse(
            JSON.stringify({
                message: 'JD fetched successfully!',
                data: apiResponse,
                success: true,
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    } catch (error: any) {
        const backendMessage =
            error?.response?.data?.message ||
            error?.response?.data ||
            error.message ||
            'Failed to fetch JD.'

        return new NextResponse(
            JSON.stringify({
                error: backendMessage,
                message: 'Failed to fetch JD.',
                success: false,
            }),
            {
                status: error?.response?.status || 500,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    }
}
