// src/services/useAuthService.ts
import axios from 'axios'
import { toast } from 'sonner'

export const useAuthService = () => {
    const sendOtp = async (
        email: string,
    ): Promise<{ success: boolean; message?: string }> => {
        try {
            const response = await axios.post('/api/candidate/send-otp', {
                email,
            })
            if (response.status === 200) {
                toast.success('OTP sent successfully! Please check your mail.')
                return { success: true }
            }
        } catch (error: any) {
            const errorMsg =
                error?.response?.data?.error ||
                error?.response?.data?.message ||
                'OTP send failed, please try later.'
            toast.error(errorMsg)
            return { success: false, message: errorMsg }
        }
        return { success: false, message: 'Unexpected error occurred' }
    }

    const verifyOtp = async (email: string, otp: string) => {
        try {
            const response = await axios.post('/api/candidate/verify-otp', {
                email,
                otp,
            })

            toast.success('Email verified successfully!')
            return { success: true, data: response.data }
        } catch (error: any) {
            const message =
                error?.response?.data?.error ||
                error?.response?.data?.message ||
                'Verification failed. Please try again.'
            toast.error(message)
            return { success: false, message }
        }
    }

    const signOut = async (): Promise<{
        success: boolean
        message?: string
    }> => {
        try {
            await axios.post('/api/candidate/sign-out')
            toast.success('Signed out successfully!')
            return { success: true }
        } catch (error: any) {
            const message =
                error?.response?.data?.error ||
                error?.response?.data?.message ||
                'Sign out failed. Please try again.'
            toast.error(message)
            return { success: false, message }
        }
    }

    return { sendOtp, verifyOtp, signOut }
}
