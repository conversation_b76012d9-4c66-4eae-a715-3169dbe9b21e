'use client'

import { useState, useRef, useCallback, useEffect } from 'react'

interface SpeechRecognition extends EventTarget {
    continuous: boolean
    interimResults: boolean
    lang: string // <--- Add this line for the 'lang' property

    onresult:
        | ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => unknown)
        | null
    onerror:
        | ((
              this: SpeechRecognition,
              ev: SpeechRecognitionErrorEvent,
          ) => unknown)
        | null
    onend: ((this: SpeechRecognition, ev: Event) => unknown) | null
    onstart: ((this: SpeechRecognition, ev: Event) => unknown) | null
    start: () => void
    stop: () => void
    abort: () => void // Add abort for robust error handling
}

interface SpeechRecognitionEvent extends Event {
    results: SpeechRecognitionResultList
    resultIndex: number
}

interface SpeechRecognitionErrorEvent extends Event {
    error: string
    // You might also want to include 'message' if your browser provides it
    // message: string;
}

declare global {
    interface Window {
        SpeechRecognition?: new () => SpeechRecognition
        webkitSpeechRecognition?: new () => SpeechRecognition
    }
}

interface SpeechRecognitionOptions {
    onSpeechStart?: () => void
    onSpeechEnd?: () => void
}

export const useSpeechRecognition = (options?: SpeechRecognitionOptions) => {
    const [transcript, setTranscript] = useState('')
    const [interimTranscript, setInterimTranscript] = useState('')
    const [isListening, setIsListening] = useState(false)
    const recognitionRef = useRef<SpeechRecognition | null>(null)

    const initializeRecognition = useCallback(() => {
        if (typeof window === 'undefined') return

        const SpeechRecognition =
            window.SpeechRecognition || window.webkitSpeechRecognition
        if (!SpeechRecognition) {
            console.warn(
                'Speech Recognition API not supported in this browser.',
            )
            return
        }

        // Only initialize if not already initialized
        if (!recognitionRef.current) {
            recognitionRef.current = new SpeechRecognition()
            recognitionRef.current.continuous = false // IMPORTANT: Set to false for single questions
            recognitionRef.current.interimResults = true
            recognitionRef.current.lang = 'en-US' // Consider setting language

            recognitionRef.current.onresult = (
                event: SpeechRecognitionEvent,
            ) => {
                let finalTranscript = ''
                let interimText = ''

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript
                    if (event.results[i].isFinal) {
                        finalTranscript += `${transcript} `
                    } else {
                        interimText += transcript
                    }
                }

                if (finalTranscript) {
                    setTranscript((prev) => prev + finalTranscript)
                    setInterimTranscript('')
                } else {
                    setInterimTranscript(interimText)
                }
            }

            recognitionRef.current.onerror = (
                event: SpeechRecognitionErrorEvent,
            ) => {
                console.error('Speech recognition error:', event.error)
                setIsListening(false) // Ensure state is updated on error
                // In case of an error, the recognition session is often dead.
                // Abort to ensure it's fully stopped and can be restarted clean.
                if (recognitionRef.current) {
                    recognitionRef.current.abort()
                }
            }

            recognitionRef.current.onstart = () => {
                setIsListening(true)
                console.warn('Speech recognition started.')
                options?.onSpeechStart?.()
            }

            recognitionRef.current.onend = () => {
                setIsListening(false)
                console.warn('Speech recognition session ended.')
                options?.onSpeechEnd?.()
            }
        }
    }, [options]) // No dependencies needed as it only sets up event handlers and recognition instance

    const startListening = useCallback(() => {
        if (!recognitionRef.current) {
            console.error(
                'SpeechRecognition instance not initialized. Cannot start listening.',
            )
            return
        }

        // **CRITICAL FIX**: Stop if already listening to prevent InvalidStateError
        if (isListening) {
            console.warn(
                'startListening called, but already listening. Stopping current session before starting new one.',
            )
            recognitionRef.current.stop() // Use stop() instead of abort() for cleaner restarts
            // We might need a very small delay here in rare cases, but usually not with stop()
        }

        // Clear previous transcripts before a new listening session begins
        setTranscript('')
        setInterimTranscript('')

        try {
            recognitionRef.current.start()
            setIsListening(true) // Optimistically set to true, onstart will confirm
        } catch (error) {
            console.error(
                'Error attempting to start speech recognition:',
                error,
            )
            setIsListening(false) // Revert state if start fails
            if (
                error instanceof DOMException &&
                error.name === 'InvalidStateError'
            ) {
                console.warn(
                    'Caught InvalidStateError during start. It was likely already running.',
                )
            }
            // You might want to re-initialize recognitionRef.current here if it's a persistent error
        }
    }, [isListening]) // `isListening` is a dependency because we read its current value

    const stopListening = useCallback(() => {
        if (recognitionRef.current && isListening) {
            // Only attempt to stop if it's actually listening
            recognitionRef.current.stop()
            // setIsListening(false) // onend event will handle setting this state
        }
    }, [isListening]) // `isListening` is a dependency here

    const clearTranscript = useCallback(() => {
        setTranscript('')
        setInterimTranscript('')
    }, [])

    useEffect(() => {
        initializeRecognition()
        return () => {
            if (recognitionRef.current && isListening) {
                // Ensure cleanup stops active recognition
                recognitionRef.current.stop()
            }
            // Do not nullify recognitionRef.current here if initializeRecognition is not idempotent and you want to reuse it.
            // However, for typical single-instance hooks, nullifying might be fine.
            // recognitionRef.current = null;
        }
    }, [initializeRecognition, isListening]) // Add isListening to dependencies for cleanup to see latest state

    return {
        transcript,
        interimTranscript,
        isListening,
        startListening,
        stopListening,
        clearTranscript,
    }
}
