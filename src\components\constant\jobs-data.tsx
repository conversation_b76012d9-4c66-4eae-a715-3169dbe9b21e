'use client'

import axios from 'axios'
import React, { useCallback, useMemo, useState } from 'react'
import JobPreview from './JobPreview'
import {
    IconBriefcase2,
    IconCalendarEvent,
    IconClock,
    IconEye,
    IconMapPin,
} from '@tabler/icons-react'
import type { RootState } from '@/redux/store'
import { useSelector } from 'react-redux'
import dayjs from 'dayjs'
import {
    Too<PERSON>ip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
import type { Interview } from '../../Models/interview'
import type { EnumItem } from '../../Models/enums'
import { useCandidateService } from '@/services/candidate.service'
import type { JDData } from '@/Models/job'

interface JobCardProps {
    job: Interview
    onTakeInterview: (data: {
        id: string
        title: string
        company: string
        location: string
    }) => void
}

const JobCard: React.FC<JobCardProps> = ({ job, onTakeInterview }) => {
    const [viewJD, setViewJD] = useState<boolean>(false)
    const [selectedJob, setSelectedJob] = useState<JDData | null>(null)
    const { fetchResume, fetchJobDescription } = useCandidateService()

    const EnumaValues = useSelector(
        (state: RootState) => state.constantReducer.DropdownEnumValues,
    )
    const workModeTypes = EnumaValues?.workModes

    const now = dayjs()
    const start = dayjs(job.startTime)
    const end = dayjs(job.endTime)

    const isBeforeStart = now.isBefore(start)
    const isAfterEnd = now.isAfter(end)
    const isInterviewActive = !isBeforeStart && !isAfterEnd

    const tooltipText = isBeforeStart
        ? `Interview not started. Starts at ${start.format('DD MMM YYYY, hh:mm A')}`
        : isAfterEnd
          ? `Interview expired. Ended at ${end.format('DD MMM YYYY, hh:mm A')}`
          : ''

    const workModeValue = useMemo(
        () =>
            workModeTypes?.find((val: EnumItem) => val.name === job?.workMode)
                ?.label || 'N/A',
        [workModeTypes, job?.workMode],
    )

    const viewResume = async (resumeId: number | string) => {
        const blob = await fetchResume(resumeId)
        if (blob) {
            const url = window.URL.createObjectURL(blob)
            window.open(url, '_blank')
        }
    }

    const viewJobDescription = async (resumeId: number | string) => {
        const jobData = await fetchJobDescription(resumeId)
        if (jobData) {
            setSelectedJob(jobData)
            setViewJD(true)
        }
    }

    const handleClosePreview = useCallback(() => {
        setViewJD(false)
        setSelectedJob(null)
    }, [])

    return (
        <>
            <div className="flex justify-between items-center bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div className="flex flex-col">
                    <div className="font-semibold text-gray-900 text-base">
                        {job.company}
                    </div>
                    <div className="text-gray-600 text-sm">{job.title}</div>
                </div>

                <div className="flex items-center gap-10 text-[13px] text-gray-600">
                    <div className="flex items-center gap-1">
                        <IconMapPin size={18} stroke={2} color="#CD580F" />
                        <span className="font-medium text-[#949494]">
                            Location:
                        </span>
                        <span>{job.location}</span>
                    </div>
                    <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-1">
                            <IconBriefcase2
                                size={18}
                                stroke={2}
                                color="#073071"
                            />
                            <span className="font-medium text-[#949494]">
                                Work Mode:
                            </span>
                            <span>{workModeValue}</span>
                        </div>
                        <div className="flex items-center gap-1">
                            <IconClock size={18} stroke={2} color="#073071" />
                            <span className="font-medium text-[#949494]">
                                Interview Duration:
                            </span>
                            <span>{job.duration}</span>
                        </div>
                    </div>

                    {/* Start & End Time */}
                    <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-1">
                            <IconCalendarEvent
                                size={18}
                                stroke={2}
                                color="#073071"
                            />
                            <span className="font-medium text-[#949494]">
                                Interview Start
                            </span>
                            <span>{start.format('DD MMM YYYY, hh:mm A')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                            <IconCalendarEvent
                                size={18}
                                stroke={2}
                                color="#073071"
                            />
                            <span className="font-medium text-[#949494]">
                                Interview End
                            </span>
                            <span>{end.format('DD MMM YYYY, hh:mm A')}</span>
                        </div>
                    </div>
                </div>

                {/* Right: Actions */}
                <div className="flex flex-col items-end gap-2">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                                        isInterviewActive
                                            ? 'bg-[#073071] hover:bg-blue-700 text-white'
                                            : 'bg-gray-400 text-white cursor-not-allowed'
                                    }`}
                                    onClick={() =>
                                        onTakeInterview({
                                            id: job.id,
                                            title: job.title,
                                            company: job.company,
                                            location: job.location,
                                        })
                                    }
                                    disabled={!isInterviewActive}
                                >
                                    Take Interview
                                </button>
                            </TooltipTrigger>
                            {!isInterviewActive && (
                                <TooltipContent className="text-xs max-w-xs">
                                    {tooltipText}
                                </TooltipContent>
                            )}
                        </Tooltip>
                    </TooltipProvider>

                    {/* Resume & JD Links */}
                    <div className="flex items-center gap-3 text-xs">
                        <span
                            className="flex items-center gap-1 text-blue-600 hover:text-blue-700 cursor-pointer"
                            onClick={() => viewResume(job.resumeId)}
                        >
                            <IconEye size={16} className="text-[#000000]" />
                            <span className="text-[#676C74]">View Resume</span>
                        </span>
                        <span
                            className="flex items-center gap-1 text-blue-600 hover:text-blue-700 cursor-pointer"
                            onClick={() => viewJobDescription(job.resumeId)}
                        >
                            <IconEye size={16} className="text-[#000000]" />
                            <span className="text-[#676C74]">
                                Job Description
                            </span>
                        </span>
                    </div>
                </div>
            </div>

            {/* JD Preview Modal */}
            {viewJD && selectedJob && (
                <JobPreview
                    job={selectedJob}
                    onClose={handleClosePreview}
                    disable={isInterviewActive}
                    company={job.company}
                    onTakeInterview={() =>
                        onTakeInterview({
                            id: job.id,
                            title: job.title,
                            company: job.company,
                            location: job.location,
                        })
                    }
                />
            )}
        </>
    )
}

export default JobCard
