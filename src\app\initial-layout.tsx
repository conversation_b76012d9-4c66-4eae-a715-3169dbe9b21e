'use client'
import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import loginImg from '@/assets/loginImage.svg'
const InitialLayout = ({ children }: any) => (
    <div className="min-h-screen w-full flex flex-col lg:flex-row bg-[#F8F8FF] dark:bg-black">
        <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-b from-[#01337A] to-[#261B39] z-0" />

            {/* Content container with proper flex layout */}
            <div className="flex flex-col justify-center items-center w-full h-full z-10 relative px-8 py-12 min-h-screen">
                {/* Main content container */}
                <div className="flex flex-col items-center justify-center space-y-8">
                    {/* Image container */}
                    <div className="flex items-center justify-center">
                        <Image
                            src={loginImg}
                            alt="Super Admin Dashboard"
                            className="rounded-lg object-contain w-full max-w-xs lg:max-w-sm h-auto"
                            width={350}
                            height={350}
                            priority
                        />
                    </div>

                    {/* Text container */}
                    <div className="w-full max-w-md">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.2 }}
                            className="text-white text-center"
                        >
                            <h2 className="text-lg lg:text-xl xl:text-2xl font-bold leading-relaxed px-4">
                                Relax, answer at your pace — we're listening.
                            </h2>
                        </motion.div>
                    </div>
                </div>
            </div>
        </div>

        <div className="w-full lg:w-1/2 flex flex-col items-center justify-center bg-[#F8F8FF] dark:bg-black p-6 lg:p-8 xl:p-12 min-h-screen lg:min-h-0">
            <motion.div
                className="lg:hidden mb-4 text-center z-20 relative"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
            />

            {children}
        </div>
    </div>
)

export default InitialLayout
