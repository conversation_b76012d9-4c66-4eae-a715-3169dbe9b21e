#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "Running pre-commit checks..."

# Run lint-staged on staged files
npx lint-staged
EXIT_CODE=$?

if [ $EXIT_CODE -ne 0 ]; then
  echo "Pre-commit checks failed! Please fix the issues before committing."
  exit $EXIT_CODE
fi

echo "Pre-commit checks passed."

# Remove any test-related commands from here if they exist and you don't want them to run
# For example, if you see something like npm test or yarn test here, remove it.