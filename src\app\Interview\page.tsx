'use client'
import { useState, useEffect, useRef } from 'react'
import { v4 as uuidv4 } from 'uuid'
import DashboardLayout from '../dashboard-layout'
import { useInterviewSocket } from './websoket' // Assuming this path is correct
import { useSearchParams } from 'next/navigation'
import InterviewPage from '@/components/InterviewCopmonets/interview-page'

export default function Page() {
    const interviewIdRef = useRef<string>('')
    const searchParams = useSearchParams()
    const interviewId = searchParams.get('interviewId') || uuidv4()
    if (!interviewIdRef.current) {
        interviewIdRef.current = interviewId
    }

    const { messages, sendMessage, ws } = useInterviewSocket({
        interviewId: interviewIdRef.current,
    })

    const [currentQuestion, setCurrentQuestion] = useState<string | null>(null)
    const [questionAudioSrc, setQuestionAudioSrc] = useState<string>('')
    const [isLoadingQuestion, setIsLoadingQuestion] = useState(true)
    const [questionTime, setQuestionTime] = useState(0)
    const [fullTranscriptChunks, setFullTranscriptChunks] = useState<
        Array<{ chunkIndex: number; transcript: string }>
    >([])
    const [isUserSpeaking, setIsUserSpeaking] = useState(false)
    const [pendingTranscript, setPendingTranscript] = useState('')

    // Ref to keep track of processed message identifiers to prevent re-processing
    const processedMessageIdentifiers = useRef(new Set<string>())

    // Keep track of the current audio URL to clean it up properly
    const currentAudioUrlRef = useRef<string>('')

    useEffect(() => {
        if (messages.length > 0) {
            const latestMessage = messages[messages.length - 1]

            // Create a unique identifier for this message
            // Using a combination of type, payload content, and timestamp for robust deduplication.
            // THIS IS THE KEY TO DEDUPLICATION: create a stable ID for each distinct message.
            let messageIdentifier = latestMessage // Default to raw message as identifier

            try {
                const parsedMessage = JSON.parse(latestMessage)
                messageIdentifier = `${parsedMessage.type}-${parsedMessage.payload.id || parsedMessage.payload.timeStamp || parsedMessage.payload.transcript}`
                // For TRANSCRIPTs, combine transcript and timestamp if ID is not available
                if (
                    parsedMessage.type === 'TRANSCRIPT' &&
                    parsedMessage.payload.transcript &&
                    parsedMessage.payload.timeStamp
                ) {
                    messageIdentifier = `${parsedMessage.type}-${parsedMessage.payload.transcript}-${parsedMessage.payload.timeStamp}`
                } else if (
                    parsedMessage.type === 'QUESTION' &&
                    parsedMessage.payload.id
                ) {
                    messageIdentifier = `${parsedMessage.type}-${parsedMessage.payload.id}`
                }

                // Check if this message has already been processed
                if (
                    processedMessageIdentifiers.current.has(messageIdentifier)
                ) {
                    console.warn(
                        'Page: Message already processed, ignoring duplicate:',
                        messageIdentifier,
                    )
                    return // Exit early, do not process this duplicate message
                }
                // Mark this message as processed
                processedMessageIdentifiers.current.add(messageIdentifier)

                // Handle incoming question
                if (
                    parsedMessage.type === 'QUESTION' &&
                    parsedMessage.payload
                ) {
                    const { questionText, questionAudio, maxDurationSeconds } =
                        parsedMessage.payload

                    // Important: When a new question comes, you generally want to clear the previous transcript
                    setFullTranscriptChunks([]) // Clear previous transcript for a new question
                    setQuestionTime(maxDurationSeconds)
                    setCurrentQuestion(questionText)
                    setIsLoadingQuestion(false)

                    if (currentAudioUrlRef.current) {
                        URL.revokeObjectURL(currentAudioUrlRef.current)
                    }

                    if (questionAudio) {
                        const audioBlob = base64ToBlob(
                            questionAudio,
                            'audio/mp3',
                        )
                        const audioUrl = URL.createObjectURL(audioBlob)
                        currentAudioUrlRef.current = audioUrl
                        setQuestionAudioSrc(audioUrl)
                    } else {
                        setQuestionAudioSrc('')
                    }
                }

                // Handle incoming transcript
                if (
                    parsedMessage.type === 'TRANSCRIPT' &&
                    parsedMessage.payload
                ) {
                    const receivedText = parsedMessage.payload.transcript
                    const speaker = parsedMessage.payload.speaker
                    const chunkIdxFromPayload = parsedMessage.payload.chunkIndex

                    // Clear pending transcript when we get actual transcript
                    setPendingTranscript('')
                    setIsUserSpeaking(false)

                    if (receivedText && typeof receivedText === 'string') {
                        let chunkIdxToUse: number
                        if (typeof chunkIdxFromPayload === 'number') {
                            chunkIdxToUse = chunkIdxFromPayload
                        } else {
                            chunkIdxToUse = fullTranscriptChunks.length
                            console.warn(
                                'Page: Backend did not provide chunkIndex for TRANSCRIPT. Using sequential index:',
                                chunkIdxToUse,
                            )
                        }

                        setFullTranscriptChunks((prev) => {
                            const filteredPrev = prev.filter(
                                (c) => c.chunkIndex !== chunkIdxToUse,
                            )

                            const updated = [
                                ...filteredPrev,
                                {
                                    chunkIndex: chunkIdxToUse,
                                    transcript: receivedText,
                                },
                            ]

                            return updated.sort(
                                (a, b) => a.chunkIndex - b.chunkIndex,
                            )
                        })
                    } else {
                        console.warn(
                            'Page: TRANSCRIPT payload invalid (missing or invalid text in "transcript" field):',
                            parsedMessage.payload,
                        )
                    }
                }
            } catch (error) {
                console.error(
                    'Page: Failed to parse message or process logic in useEffect:',
                    error,
                    latestMessage,
                )
                setIsLoadingQuestion(false)
            }
        }
    }, [messages, fullTranscriptChunks])

    const finalTranscript = fullTranscriptChunks
        .map((chunk) => chunk.transcript)
        .join(' ')

    // Cleanup for audio URLs
    useEffect(
        () => () => {
            if (currentAudioUrlRef.current) {
                URL.revokeObjectURL(currentAudioUrlRef.current)
            }
        },
        [],
    )

    // Helper function for base64 to Blob conversion
    const base64ToBlob = (base64: string, mimeType: string) => {
        const byteCharacters = atob(base64)
        const byteNumbers = new Array(byteCharacters.length)
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i)
        }
        const byteArray = new Uint8Array(byteNumbers)
        const blob = new Blob([byteArray], { type: mimeType })
        return blob
    }

    return (
        <DashboardLayout>
            <InterviewPage
                currentQuestionText={currentQuestion}
                questionAudioUrl={questionAudioSrc}
                isLoadingQuestion={isLoadingQuestion}
                sendMessage={sendMessage}
                questionTime={questionTime}
                fullTranscript={finalTranscript}
                ws={ws}
                questionIndex={1}
                isUserSpeaking={isUserSpeaking}
                pendingTranscript={pendingTranscript}
                onUserSpeakingChange={setIsUserSpeaking}
                onPendingTranscriptChange={setPendingTranscript}
            />
        </DashboardLayout>
    )
}
