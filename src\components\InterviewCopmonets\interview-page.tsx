'use client'

import {
    useState,
    useEffect,
    useLayoutEffect,
    useRef,
    useCallback,
} from 'react'
import { useRouter } from 'next/navigation'
import { useMediaRecorder } from '@/hooks/use-media-recorder'
import { useSpeechRecognition } from '@/hooks/use-speech-recognition'
import { useTextToSpeech } from '@/hooks/use-text-to-speech'
import React from 'react'

// Remove all lazy loading - import directly
import InterviewHeader from './interview-header'
import VideoSection from './video-section'
import ChatSection from './chat-section'
import VideoControls from './ video-controls'
import type { RootState } from '@/redux/store'
import { useSelector } from 'react-redux'
import type { Candidate } from '@/Models/candidate'

interface InterviewPageProps {
    currentQuestionText: string | null
    questionAudioUrl: string
    isLoadingQuestion: boolean
    sendMessage: (message: any) => void
    questionTime: number
    fullTranscript: string
    ws?: WebSocket | null
    questionIndex?: number
    isUserSpeaking?: boolean
    pendingTranscript?: string
    onUserSpeakingChange?: (speaking: boolean) => void
    onPendingTranscriptChange?: (transcript: string) => void
}

export default function InterviewPage({
    currentQuestionText,
    questionAudioUrl,
    isLoadingQuestion,
    sendMessage,
    questionTime,
    fullTranscript,
    ws,
    questionIndex = 1,
    pendingTranscript,
    isUserSpeaking,
    onUserSpeakingChange,
    onPendingTranscriptChange,
}: InterviewPageProps) {
    const router = useRouter()
    const videoRef = useRef<HTMLVideoElement>(null)
    const audioRef = useRef<HTMLAudioElement>(null)
    const [remainingTime, setRemainingTime] = useState(questionTime)
    const [isVideoElementReady, setIsVideoElementReady] = useState(false)
    const [isMediaInitialized, setIsMediaInitialized] = useState(false)
    const userData: Candidate | null = useSelector(
        (state: RootState) => state.constantReducer.logedinUser,
    )
    const [userName] = useState(userData?.candidateName || 'Candidate')
    const [videoChunks, setVideoChunks] = useState<Blob[]>([])
    const [videoPreviewUrls, setVideoPreviewUrls] = useState<string[]>([])
    // New state to track the last played audio URL
    const [lastPlayedAudioUrl, setLastPlayedAudioUrl] = useState<string | null>(
        null,
    )

    // Add a question identifier to track when questions change
    const [currentQuestionId, setCurrentQuestionId] = useState<string>('')
    const [showScreenShareModal, setShowScreenShareModal] = useState(false)
    const [screenStream, setScreenStream] = useState<MediaStream | null>(null)
    const [screenShareError, setScreenShareError] = useState<string | null>(
        null,
    )
    const [leaveCount, setLeaveCount] = useState(0)
    const [showWarningModal, setShowWarningModal] = useState(false)
    const [videoError, setVideoError] = useState<string | null>(null)

    const {
        transcript,
        interimTranscript,
        isListening,
        startListening,
        stopListening,
        clearTranscript,
    } = useSpeechRecognition({
        onSpeechStart: () => {
            onUserSpeakingChange?.(true)
        },
        onSpeechEnd: () => {
            onUserSpeakingChange?.(false)
            onPendingTranscriptChange?.('')
        },
    })

    const { speak, stopSpeaking, isSpeaking } = useTextToSpeech()
    const {
        startRecording,
        stopRecording,
        downloadRecording,
        startRecordingWithWebSocket,
    } = useMediaRecorder()

    const timeProgress = ((questionTime - remainingTime) / questionTime) * 100
    const formattedTime = `${Math.floor(remainingTime / 60)
        .toString()
        .padStart(2, '0')}:${(remainingTime % 60).toString().padStart(2, '0')}`

    // Enhanced video ref callback with proper initialization check
    const videoRefCallback = useCallback((element: HTMLVideoElement | null) => {
        if (element) {
            videoRef.current = element

            // Wait for the element to be fully rendered in the DOM
            setTimeout(() => {
                if (videoRef.current && videoRef.current.isConnected) {
                    setIsVideoElementReady(true)
                } else {
                    console.warn(
                        'Video element not yet connected to DOM, retrying...',
                    )
                    // Retry after a short delay
                    setTimeout(() => {
                        if (videoRef.current && videoRef.current.isConnected) {
                            setIsVideoElementReady(true)
                        }
                    }, 100)
                }
            }, 50) // Small delay to ensure DOM connection
        }
    }, [])

    const initializeMedia = useCallback(async () => {
        if (!videoRef.current) {
            console.error('Video ref is null in initializeMedia')
            setVideoError('Video element not found.')
            return
        }

        if (!videoRef.current.isConnected) {
            console.error('Video element is not connected to DOM')
            setVideoError('Video element not ready.')
            return
        }

        if (isMediaInitialized) {
            return
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { width: 1280, height: 720 },
                audio: true,
            })

            // Ensure video element is still available
            if (!videoRef.current) {
                console.error('Video ref became null after getting stream')
                stream.getTracks().forEach((track) => track.stop())
                return
            }

            videoRef.current.srcObject = stream
            videoRef.current.style.background = '#222'
            videoRef.current.style.border = '2px solid #4f46e5'
            videoRef.current.style.display = 'block'
            setVideoError(null)
            setIsMediaInitialized(true)

            if (ws) {
                await startRecordingWithWebSocket(stream, ws, 0, questionIndex)
            } else {
                await startRecording(stream, (chunk: Blob) => {
                    setVideoChunks((prevChunks) => [...prevChunks, chunk])
                    const url = URL.createObjectURL(chunk)
                    setVideoPreviewUrls((prevUrls) => [...prevUrls, url])
                })
            }
            setShowScreenShareModal(true)
        } catch (error) {
            console.error('Error accessing media devices:', error)
            setVideoError(
                'Could not access camera or microphone. Please check your permissions and try again.',
            )
        }
    }, [
        startRecording,
        startRecordingWithWebSocket,
        ws,
        questionIndex,
        isMediaInitialized,
    ])

    const cleanupMedia = useCallback(() => {
        if (videoRef.current?.srcObject) {
            const stream = videoRef.current.srcObject as MediaStream
            stream.getTracks().forEach((track) => track.stop())
        }
        stopRecording()
        setVideoPreviewUrls((prevUrls) => {
            prevUrls.forEach(URL.revokeObjectURL)
            return []
        })
        setVideoChunks([])
        setIsMediaInitialized(false)
    }, [stopRecording])

    // Initialize media only when video element is ready and connected
    useEffect(() => {
        if (
            isVideoElementReady &&
            !isMediaInitialized &&
            videoRef.current?.isConnected
        ) {
            initializeMedia()
        }
    }, [isVideoElementReady, isMediaInitialized, initializeMedia])

    const handleEndInterview = useCallback(() => {
        stopListening()
        stopSpeaking()
        cleanupMedia()
        downloadRecording()
        router.push('/Interview/complete')
    }, [stopListening, stopSpeaking, cleanupMedia, downloadRecording, router])

    const handleNextQuestion = useCallback(() => {
        stopListening()
        stopSpeaking()
        clearTranscript()
        const payload = JSON.stringify({ type: 'MANUAL_NEXT', payload: {} })

        sendMessage(payload)

        // Reset audio state to prepare for the next question
        setLastPlayedAudioUrl(null) // Crucial: Indicate no audio has played for the *new* question yet
    }, [stopListening, stopSpeaking, clearTranscript, sendMessage])

    const handleSkip = useCallback(() => {
        handleNextQuestion()
    }, [handleNextQuestion])

    const handleDone = useCallback(() => {
        stopListening()
        handleNextQuestion()
    }, [stopListening, handleNextQuestion])

    // Reset remainingTime when questionTime changes
    useEffect(() => {
        setRemainingTime(questionTime)
    }, [questionTime])

    // Countdown timer for remainingTime
    useEffect(() => {
        if (remainingTime <= 0) return
        const timer = setInterval(() => {
            setRemainingTime((prev) => {
                if (prev <= 1) {
                    clearInterval(timer)
                    // Optionally handle end of question here
                    return 0
                }
                return prev - 1
            })
        }, 1000)
        return () => clearInterval(timer)
    }, [remainingTime])

    // Cleanup on unmount
    useEffect(() => {
        const audioEl = audioRef.current

        return () => {
            cleanupMedia()
            stopSpeaking()

            // Clean up audio element safely using cached reference
            if (audioEl) {
                audioEl.pause()
                audioEl.src = ''
                audioEl.onended = null
                audioEl.onerror = null
            }
        }
    }, [cleanupMedia, stopSpeaking])

    // Effect to reset audio state when a new question arrives
    useEffect(() => {
        if (currentQuestionText && currentQuestionText !== currentQuestionId) {
            console.warn('New question detected, resetting audio state')
            setCurrentQuestionId(currentQuestionText)
            setLastPlayedAudioUrl(null) // Reset to allow new audio to play
        }
    }, [currentQuestionText, currentQuestionId])

    useEffect(() => {
        // Only proceed if media is initialized
        if (!isMediaInitialized) {
            return
        }

        // Condition to proceed: not loading, and we have a current question text
        if (!isLoadingQuestion && currentQuestionText) {
            // Case 1: Audio URL is provided and it's a new audio (or the URL has changed)
            if (questionAudioUrl && lastPlayedAudioUrl !== questionAudioUrl) {
                console.warn(
                    'Attempting to play new question audio from URL:',
                    questionAudioUrl,
                )

                if (audioRef.current) {
                    // Stop any current playback (both native audio and TTS)
                    stopSpeaking() // Ensure TTS is stopped
                    audioRef.current.pause()
                    audioRef.current.currentTime = 0

                    // Set up event listeners for native audio playback
                    audioRef.current.onended = () => {
                        console.warn(
                            'Native audio finished playing, starting listening and setting isUserSpeaking to 1...',
                        )
                        startListening()
                        // When AI finishes speaking, user starts speaking, so send 1
                        if (ws) {
                            startRecordingWithWebSocket(
                                videoRef.current?.srcObject as MediaStream,
                                ws,
                                1, // User is now speaking
                                questionIndex,
                            )
                        }
                    }

                    audioRef.current.onerror = (e) => {
                        console.error('Native audio playback error:', e)
                        // Fallback to TTS if native audio fails to play
                        console.warn(
                            'Falling back to browser TTS for question (native audio error):',
                            currentQuestionText,
                        )
                        speak(currentQuestionText, () => {
                            startListening()
                            // When AI finishes speaking (via TTS), user starts speaking, so send 1
                            if (ws) {
                                startRecordingWithWebSocket(
                                    videoRef.current?.srcObject as MediaStream,
                                    ws,
                                    1, // User is now speaking
                                    questionIndex,
                                )
                            }
                        })
                        setLastPlayedAudioUrl(questionAudioUrl) // Still mark as attempted
                    }

                    // Set the source and play
                    audioRef.current.src = questionAudioUrl

                    audioRef.current
                        .play()
                        .then(() => {
                            console.warn(
                                'Native audio played successfully for new question.',
                            )
                            setLastPlayedAudioUrl(questionAudioUrl)
                        })
                        .catch((e) => {
                            console.error(
                                'Error playing native audio for new question (play() promise rejected):',
                                e,
                            )
                            // Fallback to TTS if play() promise is rejected
                            console.warn(
                                'Falling back to browser TTS for question (play() promise error):',
                                currentQuestionText,
                            )
                            speak(currentQuestionText, () => {
                                startListening()
                                // When AI finishes speaking (via TTS fallback), user starts speaking, so send 1
                                if (ws) {
                                    startRecordingWithWebSocket(
                                        videoRef.current
                                            ?.srcObject as MediaStream,
                                        ws,
                                        1, // User is now speaking
                                        questionIndex,
                                    )
                                }
                            })
                            setLastPlayedAudioUrl(questionAudioUrl) // Still mark as attempted
                        })
                    // While AI is speaking, ensure isUserSpeaking is 0
                    if (ws) {
                        startRecordingWithWebSocket(
                            videoRef.current?.srcObject as MediaStream,
                            ws,
                            0, // AI is speaking
                            questionIndex,
                        )
                    }
                } else {
                    console.error(
                        'Audio ref is not available for native audio playback.',
                    )
                    // Fallback to TTS if audio ref is not available
                    console.warn(
                        'Falling back to browser TTS for question (audio ref missing):',
                        currentQuestionText,
                    )
                    speak(currentQuestionText, () => {
                        startListening()
                        // When AI finishes speaking (via TTS fallback), user starts speaking, so send 1
                        if (ws) {
                            startRecordingWithWebSocket(
                                videoRef.current?.srcObject as MediaStream,
                                ws,
                                1, // User is now speaking
                                questionIndex,
                            )
                        }
                    })
                }
            } else if (!questionAudioUrl && currentQuestionText) {
                // Case 2: No audio URL provided, use browser TTS for the current question text
                // Check if this question has already been spoken via TTS to prevent endless loops
                if (currentQuestionText === lastPlayedAudioUrl) {
                    // Re-using lastPlayedAudioUrl as a "last_spoken_text" marker
                    console.error('Question already spoken via TTS, skipping.')
                    return
                }

                console.warn(
                    'No question audio URL, using browser TTS for:',
                    currentQuestionText,
                )
                stopSpeaking() // Ensure any previous TTS is stopped
                speak(currentQuestionText, () => {
                    // After TTS finishes, start listening
                    startListening()
                    // When AI finishes speaking (via TTS), user starts speaking, so send 1
                    if (ws) {
                        startRecordingWithWebSocket(
                            videoRef.current?.srcObject as MediaStream,
                            ws,
                            1, // User is now speaking
                            questionIndex,
                        )
                    }
                })
                // Mark the current question text as "played" by TTS to prevent re-speaking
                setLastPlayedAudioUrl(currentQuestionText)
                // While AI is speaking, ensure isUserSpeaking is 0
                if (ws) {
                    startRecordingWithWebSocket(
                        videoRef.current?.srcObject as MediaStream,
                        ws,
                        0, // AI is speaking
                        questionIndex,
                    )
                }
            }
        } else if (!currentQuestionText && !isLoadingQuestion) {
            // Case 3: No question text and not loading, stop all audio and listening
            console.warn(
                'No question text available, stopping all audio and listening',
            )
            stopListening()
            stopSpeaking() // Stop any ongoing TTS
            if (audioRef.current) {
                audioRef.current.pause()
                audioRef.current.currentTime = 0
                audioRef.current.src = ''
            }
            setLastPlayedAudioUrl(null) // Reset audio state
            // When no one is speaking, ensure isUserSpeaking is 0
            if (ws && videoRef.current?.srcObject) {
                startRecordingWithWebSocket(
                    videoRef.current.srcObject as MediaStream,
                    ws,
                    0, // No one speaking
                    questionIndex,
                )
            }
        }
    }, [
        currentQuestionText,
        questionAudioUrl,
        startListening,
        isLoadingQuestion,
        stopListening,
        lastPlayedAudioUrl,
        speak,
        stopSpeaking,
        ws,
        startRecordingWithWebSocket,
        questionIndex,
        isMediaInitialized, // Add this dependency
    ])

    // Update pending transcript when interim transcript changes
    useEffect(() => {
        if (interimTranscript && isListening) {
            onPendingTranscriptChange?.(interimTranscript)
        } else if (!interimTranscript) {
            onPendingTranscriptChange?.('')
        }
    }, [interimTranscript, isListening, onPendingTranscriptChange])

    // Function to handle screen sharing
    const handleScreenShare = async () => {
        setScreenShareError(null)
        try {
            // Only allow entire screen
            const displayStream = await (
                navigator.mediaDevices as any
            ).getDisplayMedia({
                video: {
                    displaySurface: 'monitor', // Prefer entire screen
                    cursor: 'always',
                },
                audio: false,
            })
            setScreenStream(displayStream)
            setShowScreenShareModal(false)
            // Listen for stop event
            const [track] = displayStream.getVideoTracks()
            if (track) {
                track.onended = () => {
                    handleEndInterview()
                }
                track.oninactive = () => {
                    handleEndInterview()
                }
            } else {
                // Fallback: if no track, end interview
                handleEndInterview()
            }
        } catch {
            // User cancelled or error
            setScreenShareError(
                'Screen sharing was cancelled or failed. The interview will now end.',
            )
            setTimeout(() => {
                handleEndInterview()
            }, 2000)
        }
    }

    // Clean up screen stream on unmount
    useEffect(
        () => () => {
            if (screenStream) {
                screenStream.getTracks().forEach((track) => track.stop())
            }
        },
        [screenStream],
    )

    // Detect tab/window/app switching
    useEffect(() => {
        const handleVisibilityOrBlur = () => {
            // Only trigger if the page is hidden or blurred
            if (document.hidden || document.visibilityState === 'hidden') {
                setLeaveCount((prev) => {
                    const newCount = prev + 1

                    if (newCount >= 3) {
                        handleEndInterview()
                    } else {
                        setShowWarningModal(true)
                    }
                    return newCount
                })
            }
        }
        document.addEventListener('visibilitychange', handleVisibilityOrBlur)
        window.addEventListener('blur', handleVisibilityOrBlur)
        return () => {
            document.removeEventListener(
                'visibilitychange',
                handleVisibilityOrBlur,
            )
            window.removeEventListener('blur', handleVisibilityOrBlur)
        }
    }, [handleEndInterview])

    // Hide warning modal when user returns - but with a delay
    useEffect(() => {
        const handleFocus = () => {
            // Add a delay before hiding the modal so user can see it
            setTimeout(() => {
                setShowWarningModal(false)
            }, 2000) // Show modal for 2 seconds after user returns
        }
        window.addEventListener('focus', handleFocus)
        return () => {
            window.removeEventListener('focus', handleFocus)
        }
    }, [])

    if (isLoadingQuestion) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center p-8 bg-white rounded-lg shadow-md">
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500 mx-auto" />
                    <p className="mt-4 text-lg text-gray-700">
                        Preparing your interview, please wait...
                    </p>
                </div>
            </div>
        )
    }
    // Fallback if video is not available
    const videoUnavailable =
        !videoRef.current ||
        !(videoRef.current.srcObject instanceof MediaStream)

    return (
        <div className="min-h-screen">
            {/* Screen Share Modal */}
            {showScreenShareModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
                        <h2 className="text-xl font-semibold mb-4">
                            Share Your Entire Screen
                        </h2>
                        <p className="mb-4 text-gray-700">
                            Please share your <b>entire screen</b> to proceed
                            with the interview. If you stop sharing, the
                            interview will end automatically.
                        </p>
                        {screenShareError && (
                            <div className="mb-4 text-red-600">
                                {screenShareError}
                            </div>
                        )}
                        <button
                            className="bg-purple-600 text-white px-6 py-2 rounded hover:bg-purple-700 transition"
                            onClick={handleScreenShare}
                        >
                            Share Entire Screen
                        </button>
                    </div>
                </div>
            )}
            {/* Warning Modal for tab/app switching */}
            {showWarningModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
                        <h2 className="text-xl font-semibold mb-4 text-red-600">
                            Warning
                        </h2>
                        <p className="mb-4 text-gray-700">
                            You have switched tabs, windows, or applications
                            during the interview.
                            <br />
                            Please stay on this page. If you leave{' '}
                            <b>{3 - leaveCount}</b> more time(s), the interview
                            will end automatically.
                        </p>
                        <button
                            className="bg-purple-600 text-white px-6 py-2 rounded hover:bg-purple-700 transition"
                            onClick={() => setShowWarningModal(false)}
                        >
                            Continue Interview
                        </button>
                    </div>
                </div>
            )}
            <div className="mx-auto p-6">
                <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
                    <InterviewHeader
                        remainingTime={formattedTime}
                        timeProgress={timeProgress}
                    />

                    <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
                        <div className="lg:col-span-3">
                            <VideoSection
                                ref={videoRefCallback}
                                userName={userName}
                            />
                            {videoError && (
                                <div className="text-red-600 mt-2">
                                    {videoError}
                                </div>
                            )}
                            {!isVideoElementReady && (
                                <div className="text-blue-600 mt-2">
                                    Initializing video element...
                                </div>
                            )}
                            {isVideoElementReady && !isMediaInitialized && (
                                <div className="text-orange-600 mt-2">
                                    Setting up camera and microphone...
                                </div>
                            )}
                        </div>

                        <div className="lg:col-span-2">
                            <ChatSection
                                currentQuestion={
                                    currentQuestionText ||
                                    'Waiting for question...'
                                }
                                transcript={fullTranscript}
                                interimTranscript={interimTranscript}
                                isListening={isListening}
                                isSpeaking={isSpeaking}
                                isUserSpeaking={isUserSpeaking}
                                pendingTranscript={pendingTranscript}
                            />
                        </div>
                    </div>

                    {questionAudioUrl && (
                        <audio
                            ref={audioRef}
                            src={questionAudioUrl}
                            style={{ display: 'none' }}
                            preload="auto"
                        />
                    )}

                    <VideoControls
                        onEndInterview={handleEndInterview}
                        onSkip={handleSkip}
                        onDone={handleDone}
                        isListening={isListening}
                        isSpeaking={isSpeaking}
                    />
                </div>
            </div>
        </div>
    )
}
