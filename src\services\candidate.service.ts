import { useState, useCallback, useMemo } from 'react'
import axios from 'axios'
import { handleApiError } from '@/lib/errorHandler'
import type { Candidate } from '@/Models/candidate'
import type { JDData } from '@/Models/job'

export const useCandidateService = () => {
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const fetchResume = useCallback(
        async (resumeId: number | string): Promise<Blob | null> => {
            setLoading(true)
            setError(null)
            try {
                const res = await axios.post(
                    '/api/candidate/resume',
                    { resumeId },
                    {
                        responseType: 'blob',
                    },
                )

                return new Blob([res.data], {
                    type: res.headers['content-type'] || 'application/pdf',
                })
            } catch (err) {
                setError(handleApiError(err, 'Could not load resume.'))
                return null
            } finally {
                setLoading(false)
            }
        },
        [],
    )

    const fetchJobDescription = useCallback(
        async (resumeId: number | string): Promise<JDData | null> => {
            setLoading(true)
            setError(null)
            try {
                const res = await axios.post('/api/candidate/jd-details', {
                    resumeId,
                })
                return res.data.data
            } catch (err) {
                setError(
                    handleApiError(err, 'Could not fetch job description.'),
                )
                return null
            } finally {
                setLoading(false)
            }
        },
        [],
    )

    const fetchCandidateInterviews = useCallback(
        async (
            page: number,
            size: number,
            searchVal: string,
        ): Promise<{ jobs: any[]; total: number }> => {
            setLoading(true)
            setError(null)
            try {
                const requestBody = {
                    page,
                    size,
                    name: searchVal,
                }

                const response = await axios.post(
                    '/api/candidate/interviews',
                    requestBody,
                )

                if (response.status === 200) {
                    const data = response.data
                    const jobs = (data?.data?.content || []).map(
                        (job: any) => ({
                            ...job,
                            title: job.jobTitle,
                            company: job.companyName,
                            location: job.location || 'Bengaluru',
                            workMode: job.workMode || 'Remote',
                            duration: job.interviewDuration
                                ? `${job.interviewDuration} min`
                                : '60 min',
                        }),
                    )

                    return {
                        jobs,
                        total: data?.data?.totalElements || 0,
                    }
                } else {
                    throw new Error(
                        `Unexpected response status: ${response.status}`,
                    )
                }
            } catch (err) {
                setError(handleApiError(err, 'Failed to fetch jobs.'))
                return { jobs: [], total: 0 }
            } finally {
                setLoading(false)
            }
        },
        [],
    )

    const fetchCandidateDetails = useCallback(async (): Promise<Candidate> => {
        setLoading(true)
        setError(null)
        try {
            const res = await axios.post('/api/candidate')
            return res.data.data
        } catch (err) {
            setError(handleApiError(err, 'Failed to fetch candidate details.'))
            throw err
        } finally {
            setLoading(false)
        }
    }, [])

    // You can also memoize the return object if you prefer stable reference
    return useMemo(
        () => ({
            fetchResume,
            fetchJobDescription,
            fetchCandidateInterviews,
            fetchCandidateDetails,
            loading,
            error,
        }),
        [
            fetchResume,
            fetchJobDescription,
            fetchCandidateInterviews,
            fetchCandidateDetails,
            loading,
            error,
        ],
    )
}
