import React from 'react'

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label: string
    error?: string
}

const FormInput: React.FC<FormInputProps> = ({ label, error, ...rest }) => (
    <div className="mb-4">
        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
            {label} {rest.required && <span className="text-red-500">*</span>}
        </label>
        <input
            {...rest}
            className={`w-full p-4 border ${
                error ? 'border-red-500' : 'border-gray-300'
            } rounded-md text-gray-700 dark:text-black`}
        />
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
    </div>
)

export default FormInput
