'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Avatar, AvatarFallback } from '../ui/avatar'
import MobileHeader from '../constant/MobileHeader'
import { useRouter } from 'next/navigation'
import { useDispatch, useSelector } from 'react-redux'
import { IconLogout } from '@tabler/icons-react'
import type { RootState } from '@/redux/store'
import { setDropdownEnumValues, setLogedinUser } from '@/redux/constSlice'
import type { Candidate } from '../../Models/candidate'
import { useAuthService } from '@/services/auth.service'
import { useEnumService } from '@/services/enum.service'

const Header: React.FC = () => {
    const [isMenuOpen, setMenuOpen] = useState(false)
    const [isMobile, setIsMobile] = useState(false)
    const userData: Candidate | null = useSelector(
        (state: RootState) => state.constantReducer.logedinUser,
    )

    const router = useRouter()
    const dispatch = useDispatch()
    const { signOut } = useAuthService()
    const { fetchEnums } = useEnumService()

    useEffect(() => {
        const loadEnums = async () => {
            const result = await fetchEnums()
            if (result.success) {
                dispatch(setDropdownEnumValues(result.data))
            }
        }
        loadEnums()
    }, [dispatch, fetchEnums])

    useEffect(() => {
        const handleResize = () => setIsMobile(window.innerWidth <= 768)
        window.addEventListener('resize', handleResize)
        handleResize()
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    const handleSignOut = async () => {
        const result = await signOut()
        if (result.success) {
            router.push('/')
            setMenuOpen(false)
            dispatch(setLogedinUser(null))
        }
    }

    const SignoutModal = () => (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 z-50">
            <p className="text-gray-500 dark:text-gray-400 text-center text-sm mb-3">
                {userData?.candidateEmail || 'Loading...'}
            </p>
            <div className="flex justify-center mb-4">
                <div className="relative">
                    <Avatar>
                        <AvatarFallback className="w-10 h-10 bg-amber-600 text-white text-lg flex items-center justify-center rounded-full">
                            {userData?.candidateName
                                ?.charAt(0)
                                ?.toUpperCase() || 'U'}
                        </AvatarFallback>
                    </Avatar>
                </div>
            </div>

            <h1 className="text-lg font-semibold text-gray-800 text-center dark:text-white mb-4">
                Hi, {userData?.candidateName || 'User'}
            </h1>
            <button
                onClick={handleSignOut}
                className="w-full flex items-center justify-center gap-2 py-2 px-4
                bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600
                text-gray-700 dark:text-gray-200 rounded-lg transition-colors"
            >
                <IconLogout size={18} />
                Sign out
            </button>
        </div>
    )

    return (
        <motion.div
            initial={{ y: 0 }}
            animate={{ y: 0 }}
            transition={{ type: 'spring', stiffness: 100, damping: 20 }}
        >
            {isMobile ? (
                <MobileHeader />
            ) : (
                <header className="fixed top-0 left-0 right-0 z-10 bg-white dark:bg-gray-900 w-full mb-4 pt-4 px-4">
                    <div className="flex items-center mb-4 justify-between relative">
                        <Link
                            href="/dashboard"
                            className="flex items-center space-x-2"
                        >
                            <div className="w-8 h-8 bg-blue-900 rounded" />
                            <span className="font-bold dark:text-white">
                                Logo
                            </span>
                        </Link>

                        <div className="flex items-center space-x-6">
                            <div className="relative">
                                <button
                                    onClick={() => setMenuOpen(!isMenuOpen)}
                                    className="text-black dark:text-white flex items-center"
                                >
                                    <Avatar>
                                        <AvatarFallback className="w-10 h-10 bg-amber-600 text-white text-lg flex items-center justify-center rounded-full">
                                            {userData?.candidateName
                                                ?.charAt(0)
                                                ?.toUpperCase() || 'U'}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div className="ml-2 text-sm text-left">
                                        <div className="font-semibold">
                                            {userData?.candidateName || 'User'}
                                        </div>
                                        <div className="text-xs text-gray-500 dark:text-gray-400">
                                            Candidate
                                        </div>
                                    </div>
                                </button>
                                {isMenuOpen && <SignoutModal />}
                            </div>
                        </div>
                    </div>
                </header>
            )}
        </motion.div>
    )
}

export default Header
