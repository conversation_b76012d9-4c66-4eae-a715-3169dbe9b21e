import { NextResponse } from 'next/server'
import axios from 'axios'

const API_URL = process.env.NEXT_PUBLIC_SUPER_ADMIN_HOST

export async function POST(req: Request) {
    try {
        const apiResponse = await axios.get(`${API_URL}/enums/getAll`, {
            headers: {
                'Content-Type': 'application/json',
            },
        })

        return new NextResponse(
            JSON.stringify({
                message: 'OTP sent successfully!',
                data: apiResponse.data,
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    } catch (error: any) {
        // Try to extract the backend error message
        const backendMessage =
            error?.response?.data ||
            error?.response?.data?.error ||
            error.message ||
            'OTP sending failed'

        return new NextResponse(JSON.stringify({ error: backendMessage }), {
            status: error?.response?.status || 500,
            headers: { 'Content-Type': 'application/json' },
        })
    }
}
