'use client' // This directive marks the component as a client component

import store, { persistor } from '@/redux/store'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'

const ClientProvider = ({ children }: any) => (
    <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
            {children}
        </PersistGate>
    </Provider>
)

export default ClientProvider
