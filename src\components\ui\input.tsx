import * as React from 'react'
import { cn } from '@/lib/utils'

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
    ({ className, type, ...props }, ref) => (
        <input
            type={type}
            className={cn(
                'flex h-9 w-full rounded-1xl border border-[1px] border-[#D8D8D8] bg-white  dark:bg-[#2D3748] px-3 py-1 text-base shadow-sm transition-colors',
                'file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground',
                'focus:outline-none focus:ring-2 focus:ring-[#073071] focus:border-[#073071] focus:bg-white',
                'disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
                className,
            )}
            ref={ref}
            {...props}
        />
    ),
)
Input.displayName = 'Input'

export { Input }
