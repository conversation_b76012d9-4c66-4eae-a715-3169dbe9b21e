import { useCallback, useEffect, useRef, useState } from 'react'

type InterviewSocket = {
    interviewId: string
}
const WEB_SOCKET_HOST = process.env.NEXT_PUBLIC_WEB_SOCKET_HOST

export const useInterviewSocket = ({ interviewId }: InterviewSocket) => {
    const [messages, setMessages] = useState<string[]>([])
    const [isConnected, setIsConnected] = useState(false)
    const [socketError, setSocketError] = useState<string | null>(null)
    const socketRef = useRef<WebSocket | null>(null)
    const reconnectTimeout = useRef<NodeJS.Timeout | null>(null)
    const isConnectingRef = useRef(false)
    const currentInterviewIdRef = useRef<string>('')

    const connectWebSocket = useCallback(() => {
        const currentInterviewId = currentInterviewIdRef.current

        if (!currentInterviewId || isConnectingRef.current) {
            return
        }

        // Check if we already have an open connection for this interview
        if (socketRef.current?.readyState === WebSocket.OPEN) {
            return
        }

        isConnectingRef.current = true

        // Close existing connection if any
        if (
            socketRef.current &&
            socketRef.current.readyState !== WebSocket.CLOSED
        ) {
            socketRef.current.close(1000)
            socketRef.current = null
        }

        const socket = new WebSocket(
            `${WEB_SOCKET_HOST}/interview?interview-id=${currentInterviewId}`,
        )

        socketRef.current = socket

        socket.onopen = () => {
            console.warn('✅ WebSocket connected')
            setIsConnected(true)
            setSocketError(null)
            isConnectingRef.current = false
            socket.send(
                JSON.stringify({
                    type: 'hello',
                    interviewId: currentInterviewId,
                }),
            )
        }

        socket.onmessage = (event) => {
            const data = event.data
            console.warn('📩 Received:', data)
            setMessages((prev) => [...prev, data])
        }

        socket.onerror = (error: Event) => {
            console.error('❌ WebSocket error event:', error)
            setSocketError('WebSocket connection error')
            isConnectingRef.current = false
        }

        socket.onclose = (event) => {
            console.warn(
                '🔌 WebSocket disconnected',
                event.reason || '',
                event.code,
            )
            setIsConnected(false)
            isConnectingRef.current = false

            // Only retry if not manually closed and interview ID hasn't changed
            if (
                event.code !== 1000 &&
                currentInterviewIdRef.current === currentInterviewId
            ) {
                reconnectTimeout.current = setTimeout(() => {
                    if (currentInterviewIdRef.current === currentInterviewId) {
                        connectWebSocket()
                    }
                }, 3000)
            }
        }
    }, [])

    useEffect(() => {
        if (!interviewId) return

        // Update the current interview ID
        currentInterviewIdRef.current = interviewId

        // Clear any existing reconnection timeout
        if (reconnectTimeout.current) {
            clearTimeout(reconnectTimeout.current)
            reconnectTimeout.current = null
        }

        // Connect with a small delay to ensure component is fully mounted
        const connectTimer = setTimeout(() => {
            connectWebSocket()
        }, 100)

        return () => {
            clearTimeout(connectTimer)
            if (reconnectTimeout.current) {
                clearTimeout(reconnectTimeout.current)
                reconnectTimeout.current = null
            }

            // Only close if this is the current interview
            if (
                socketRef.current &&
                currentInterviewIdRef.current === interviewId
            ) {
                socketRef.current.close(1000)
                socketRef.current = null
            }
            isConnectingRef.current = false
        }
    }, [interviewId, connectWebSocket])

    const sendMessage = (msg: string) => {
        if (socketRef.current?.readyState === WebSocket.OPEN) {
            socketRef.current.send(msg)
        } else {
            console.warn('❌ WebSocket not open, message not sent')
        }
    }

    return {
        messages,
        sendMessage,
        isConnected,
        socketError,
        ws: socketRef.current, // ✅ add this line
    }
}
