'use client'

import { motion } from 'framer-motion'
import React, { useEffect, useRef, useState } from 'react'
import {
    Briefcase,
    Calendar,
    Clock,
    GraduationCap,
    UploadIcon,
    User, // Note: User is imported but not used in the provided code.
} from 'lucide-react'
import { useSelector } from 'react-redux'
import { Pencil } from 'lucide-react'
import axios from 'axios'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import activeJD from '@/assets/ActiveJD.svg'
import type { RootState } from '@/redux/store'
import dayjs from 'dayjs'
import { formatSalaryRange } from '@/lib/number-format'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

import { IconArrowRight } from '@tabler/icons-react'
import type { JDData } from '@/Models/job'

// Initialize dayjs plugins
dayjs.extend(utc)
dayjs.extend(timezone)

interface JobPreviewProps {
    job: JDData
    onClose: () => void
    type?: string // Type can be 'template' or undefined
    disable?: boolean
    company?: string
    onTakeInterview?: () => void
}

const formatDateTime = (
    iso: string | number | Date | dayjs.Dayjs | null | undefined,
) => {
    if (!iso) return 'Not available'

    try {
        // Convert UTC to local time for display
        const date = dayjs.utc(iso).local()

        // Only show time if original string had time component
        const hasTime = typeof iso === 'string' ? iso.includes('T') : true

        return hasTime
            ? date.format('dddd, MMM D [at] h:mm A') // With time
            : date.format('dddd, MMM D') // Date only
    } catch (error) {
        console.error('Error formatting date:', error)
        return 'Invalid date'
    }
}

const JobPreview: React.FC<JobPreviewProps> = ({
    job: initialJob,
    onClose,
    type,
    disable,
    company,
    onTakeInterview,
}) => {
    const modalRef = useRef<HTMLDivElement>(null)
    const router = useRouter()

    const masterDataValues = useSelector(
        (state: RootState) => state.constantReducer.DropdownEnumValues,
    )
    const workModeTypes = masterDataValues?.workModes

    // Local state for job, so we can update it after activation
    const [job, setJob] = useState<JDData>(initialJob)

    // const isJobInactive = job?.active === false;

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    }

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: 'spring',
                stiffness: 100,
            },
        },
    }
    const handleNavigation = () => {
        router.push(`/Interview/device-testing`)
    }
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                modalRef.current &&
                !modalRef.current.contains(event.target as Node)
            ) {
                onClose()
            }
        }
        document.addEventListener('mousedown', handleClickOutside)
        return () =>
            document.removeEventListener('mousedown', handleClickOutside)
    }, [onClose])

    const mustHaveCertsArr =
        job?.educationInfo?.jobCertifications
            ?.filter((cert) => cert.skillType === 'MUST_TO_HAVE')
            .map((cert) => cert.certificationsName) || []
    const mustHaveCerts =
        mustHaveCertsArr.length > 0
            ? mustHaveCertsArr.join(', ')
            : 'No must have certifications'

    const goodToHaveCertsArr =
        job?.educationInfo?.jobCertifications
            ?.filter((cert) => cert.skillType === 'GOOD_TO_HAVE')
            .map((cert) => cert.certificationsName) || []
    const goodToHaveCerts =
        goodToHaveCertsArr.length > 0
            ? goodToHaveCertsArr.join(', ')
            : 'No good to have certifications'
    const handleContinue = () => {
        if (onTakeInterview && disable) {
            onTakeInterview()
        }
    }

    return (
        <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
            <motion.div
                ref={modalRef}
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 40 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-lg w-full max-w-4xl mx-auto shadow-lg"
            >
                <div className="w-5xl max-w-6xl rounded-lg p-6 overflow-y-auto max-h-[90vh]">
                    <motion.div
                        initial="hidden"
                        animate="visible"
                        variants={containerVariants}
                        className="space-y-8 "
                    >
                        <div className="flex justify-between items-center pb-3 px-4">
                            <h2 className="text-xl font-semibold">
                                Job Preview
                            </h2>
                            <button
                                className={`flex items-center gap-2 rounded-md text-xs font-medium  transition-colors  px-6 py-2 ${
                                    disable
                                        ? 'bg-[#073071] hover:bg-blue-700 text-white'
                                        : 'bg-gray-400 text-white cursor-not-allowed'
                                }`}
                                onClick={handleContinue}
                                disabled={!disable}
                            >
                                Continue
                                <IconArrowRight size={18} />
                            </button>
                        </div>
                    </motion.div>
                    <div className="flex items-center gap-2 px-4">
                        <p className="text-darkgray10 font-medium">
                            Job Expiry Date:
                        </p>
                        <span className="ml-2 text-gray-600">
                            {job?.expiryFromDateAndTime &&
                            job?.expiryToDateAndTime
                                ? `${formatDateTime(job.expiryFromDateAndTime)} to ${formatDateTime(
                                      job.expiryToDateAndTime,
                                  )}`
                                : 'Not available'}
                        </span>
                    </div>
                    <div className="container mx-auto px-4 py-8 max-w-4xl">
                        <motion.div
                            initial="hidden"
                            animate="visible"
                            variants={containerVariants}
                            className="space-y-8"
                        >
                            {/* Job Title Section */}
                            <motion.div
                                variants={itemVariants}
                                className="space-y-2"
                            >
                                <h2 className="text-xl md:text-2xl font-semibold text-blue140">
                                    {job?.title || 'No job title specified'}
                                </h2>
                                <p className="text-darkgray10">
                                    {company || 'No company name specified'}
                                    {'- '}
                                    {job?.jobLocation ||
                                        'No job location specified'}
                                </p>
                            </motion.div>

                            {/* Job Details Grid */}
                            <motion.div
                                variants={itemVariants}
                                className="grid grid-cols-1 md:grid-cols-2 gap-6"
                            >
                                <div className="space-y-6">
                                    <div className="flex items-start gap-3">
                                        <span className="text-blue10">₹</span>
                                        <div className="flex items-center">
                                            <span className="text-darkgray10 font-medium min-w-[110px]">
                                                Annual Salary
                                            </span>
                                            <span className="ml-2 font-medium text-blue140">
                                                {formatSalaryRange(
                                                    job?.annualSalaryMin,
                                                    job?.annualSalaryMax,
                                                )}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3">
                                        <Briefcase className="text-blue10 h-5 w-5" />
                                        <div className="flex items-center">
                                            <span className="text-darkgray10 font-medium min-w-[110px]">
                                                Work Mode
                                            </span>
                                            <span className="font-medium text-blue140">
                                                {workModeTypes?.find(
                                                    (typeItem) =>
                                                        typeItem.name ===
                                                        job?.workMode,
                                                )?.label ||
                                                    'No work mode specified'}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3">
                                        <Calendar className="text-blue10 h-5 w-5" />
                                        <p className="text-darkgray10 font-medium">
                                            Experience
                                        </p>
                                        <span className="text-blue140 font-medium">
                                            {job?.experienceMin != null &&
                                            job?.experienceMax != null
                                                ? `${job.experienceMin} to ${job.experienceMax} Years`
                                                : 'No experience specified'}
                                        </span>
                                    </div>
                                </div>

                                <div className="space-y-6">
                                    <div className="flex items-start gap-3">
                                        <Clock className="text-blue10 h-5 w-5" />
                                        <div className="flex items-center">
                                            <span className="text-darkgray10 font-medium min-w-[110px]">
                                                Notice Period
                                            </span>
                                            <span className="ml-2 font-medium text-blue140">
                                                {job?.noticePeriod
                                                    ? `${job.noticePeriod} days`
                                                    : 'No notice period specified'}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3">
                                        <GraduationCap className="text-blue10 h-5 w-5" />
                                        <div>
                                            <div className="flex items-center">
                                                <span className="text-darkgray10 font-medium min-w-[90px]">
                                                    Education:
                                                </span>
                                                <span className="ml-2 text-blue140 font-medium">
                                                    {job?.educationInfo
                                                        ?.highestQualification ||
                                                        'No education specified'}
                                                </span>
                                            </div>
                                            <div className="flex items-center mt-1">
                                                <span className="text-darkgray10 font-medium min-w-[90px]" />{' '}
                                                {/* empty span */}
                                                <span
                                                    className={`ml-2 font-medium ${job?.educationInfo?.percentage ? 'text-blue140' : 'text-darkgray10'}`}
                                                >
                                                    {job?.educationInfo
                                                        ?.percentage ||
                                                        'No percentage/CGPA specified'}
                                                    {job?.educationInfo
                                                        ?.percentage && (
                                                        <span className="ml-2">
                                                            Percentage /
                                                            CGPA{' '}
                                                        </span>
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Required Skills */}
                            <motion.div
                                variants={itemVariants}
                                className="space-y-4"
                            >
                                <h2 className="text-lg font-semibold text-blue140">
                                    {' '}
                                    Skills
                                </h2>
                                <div className="flex items-center">
                                    <h3 className="text-lg font-semibold text-blue140">
                                        Must Have Skills
                                    </h3>
                                    <div className="flex ml-5 items-center gap-4 flex-wrap">
                                        <div className="flex items-center gap-2">
                                            <span className="h-3 w-3 rounded-full bg-mediumgreen" />{' '}
                                            {/* Green */}
                                            <span className="text-sm text-darkgray10">
                                                Advanced
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="h-3 w-3 rounded-full bg-orange20" />{' '}
                                            {/* Orange */}
                                            <span className="text-sm text-darkgray10">
                                                Intermediate
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="h-3 w-3 rounded-full bg-blue150" />{' '}
                                            {/* Blue */}
                                            <span className="text-sm text-darkgray10">
                                                Beginner
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex flex-wrap gap-2">
                                    {(() => {
                                        const mustHaveSkills =
                                            job?.skills?.filter(
                                                (skill) =>
                                                    skill.skillType ===
                                                    'MUST_TO_HAVE',
                                            ) || []
                                        if (mustHaveSkills.length === 0) {
                                            return (
                                                <span className="text-darkgray10">
                                                    No must have skills
                                                </span>
                                            )
                                        }
                                        return mustHaveSkills.map(
                                            (skill, index: number) => {
                                                let bgColor = ''
                                                switch (skill.skillLevel) {
                                                    case 'ADVANCED':
                                                        bgColor =
                                                            'bg-mediumgreen' // Light green
                                                        break
                                                    case 'INTERMEDIATE':
                                                        bgColor = 'bg-orange30' // Light orange
                                                        break
                                                    case 'BEGINNER':
                                                        bgColor = 'bg-blue160' // Light blue
                                                        break
                                                    default:
                                                        bgColor = 'bg-gray-200'
                                                }
                                                return (
                                                    <span
                                                        key={index}
                                                        className={`px-4 py-1.5 rounded-full text-sm text-white ${bgColor}`}
                                                    >
                                                        {skill.skillName}
                                                    </span>
                                                )
                                            },
                                        )
                                    })()}
                                </div>
                            </motion.div>

                            {/* Keyword Search */}
                            <motion.div
                                variants={itemVariants}
                                className="space-y-4"
                            >
                                <h3 className="text-lg font-semibold text-blue140">
                                    Good To Have Skills
                                </h3>
                                <div className="flex flex-wrap gap-2">
                                    {(() => {
                                        const goodToHaveSkills =
                                            job?.skills?.filter(
                                                (skill) =>
                                                    skill.skillType ===
                                                    'GOOD_TO_HAVE',
                                            ) || []
                                        if (goodToHaveSkills.length === 0) {
                                            return (
                                                <span className="text-darkgray10">
                                                    No good to have skills
                                                </span>
                                            )
                                        }
                                        return goodToHaveSkills.map(
                                            (skill, index) => (
                                                <span
                                                    key={index}
                                                    className="px-4 py-1.5 rounded-full text-sm bg-gray-200"
                                                >
                                                    {skill.skillName}
                                                </span>
                                            ),
                                        )
                                    })()}
                                </div>
                            </motion.div>
                            <motion.div
                                variants={itemVariants}
                                className="space-y-4"
                            >
                                <h3 className="text-lg font-semibold text-blue140">
                                    Certifications
                                </h3>

                                <div className="space-y-2">
                                    <div className="flex">
                                        <span className="font-medium text-blue140 min-w-[120px]">
                                            Must have:
                                        </span>
                                        <span className="text-darkgray10 leading-relaxed">
                                            {mustHaveCerts}
                                        </span>
                                    </div>
                                    <div className="flex">
                                        <span className="font-medium text-blue140 min-w-[120px]">
                                            Good to have:
                                        </span>
                                        <span className="text-darkgray10 leading-relaxed">
                                            {goodToHaveCerts}
                                        </span>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Job Description */}
                            <motion.div
                                variants={itemVariants}
                                className="space-y-4"
                            >
                                <h3 className="text-lg font-semibold text-blue140">
                                    Job Description
                                </h3>
                                {job?.detailedJobDescription?.trim() ? (
                                    <div className="text-darkgray10 leading-relaxed whitespace-pre-line">
                                        {job.detailedJobDescription}
                                    </div>
                                ) : (
                                    <p className="text-gray-400">
                                        No job description provided.
                                    </p>
                                )}
                            </motion.div>
                        </motion.div>
                    </div>
                </div>
            </motion.div>
        </div>
    )
}

export default JobPreview
