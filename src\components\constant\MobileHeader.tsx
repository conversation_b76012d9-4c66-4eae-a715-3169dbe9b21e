'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'

import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'

const MobileHeader: React.FC = () => {
    const [isMenuOpen, setMenuOpen] = useState<boolean>(false)

    const handleMenuToggle = () => setMenuOpen(!isMenuOpen)

    return (
        <motion.header
            className="fixed top-0 left-0 right-0 z-20 bg-white dark:bg-gray-900 w-full shadow-md"
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            transition={{ type: 'spring', stiffness: 100, damping: 20 }}
        >
            <div className="flex items-center justify-between px-4 py-3">
                {/* Sidebar Toggle */}
                <div className="flex items-center space-x-3">
                    <Link href="/" className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-blue-900 rounded" />
                        <span className="font-bold text-black dark:text-white">
                            Logo
                        </span>
                    </Link>
                </div>

                {/* Notification & Profile */}
                <div className="flex items-center space-x-4">
                    {/* Profile Menu */}
                    <div className="relative">
                        <button
                            onClick={handleMenuToggle}
                            className="flex items-center"
                        >
                            <Avatar className="h-9 w-9">
                                <AvatarImage
                                    src="/placeholder.svg?height=32&width=32"
                                    alt="User"
                                />
                                <AvatarFallback>SW</AvatarFallback>
                            </Avatar>
                        </button>
                        {isMenuOpen && (
                            <div className="absolute right-0 mt-3 w-48 bg-white dark:bg-gray-700 text-black dark:text-white rounded-lg shadow-lg">
                                <button className="w-full px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-600">
                                    Profile
                                </button>
                                <button className="w-full px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-600">
                                    Logout
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </motion.header>
    )
}

export default MobileHeader
