import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { apiHelper } from '@/lib/apiHelper'

export async function POST(request: NextRequest) {
    try {
        const apiResponse = await apiHelper(`api/candidate`, {
            method: 'GET',
            request,
            //   body: apiBody.body,
        })

        return new NextResponse(
            JSON.stringify({
                message: 'candidate fetched successfully!',
                data: apiResponse,
                success: true,
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    } catch (error: any) {
        console.log('🚨 [API ROUTE] Error caught:', error)

        // Handle custom UNAUTHORIZED_SESSION_EXPIRED error
        if (
            error instanceof Error &&
            error.message === 'UNAUTHORIZED_SESSION_EXPIRED'
        ) {
            return new NextResponse(
                JSON.stringify({
                    error: 'Session expired',
                    message: 'Your session has expired. Please log in again.',
                    success: false,
                }),
                {
                    status: 401,
                    headers: { 'Content-Type': 'application/json' },
                },
            )
        }

        const backendMessage =
            error?.response?.data ||
            error?.response?.data?.message ||
            error?.response?.data ||
            error.message ||
            'Failed to fetch candidate.'

        return new NextResponse(
            JSON.stringify({
                error: backendMessage,
                message: 'Failed to fetch candidate.',
                success: false,
            }),
            {
                status: error?.response?.status || 500,
                headers: { 'Content-Type': 'application/json' },
            },
        )
    }
}
