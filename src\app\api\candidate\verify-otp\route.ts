import { NextResponse } from 'next/server'
import axios from 'axios'

const API_URL = process.env.NEXT_PUBLIC_SUPER_ADMIN_HOST

export async function POST(req: Request) {
    try {
        const body = await req.json()

        const apiResponse = await axios.post(
            `${API_URL}/candidate/verify-otp`,
            body,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        )

        // Extract the token from the response
        const token = apiResponse.data?.content

        // Set the cookie
        const response = new NextResponse(
            JSON.stringify({
                message: 'OTP verify successfully!',
                data: apiResponse.data,
            }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Set-Cookie': `Interview_toke=${token}; Path=/; HttpOnly; SameSite=Strict; Max-Age=604800`, // 7 days
                },
            },
        )
        return response
    } catch (error: any) {
        // Try to extract the backend error message
        const backendMessage =
            error?.response?.data ||
            error?.response?.data?.error ||
            error.message ||
            'OTP verify failed'

        return new NextResponse(JSON.stringify({ error: backendMessage }), {
            status: error?.response?.status || 500,
            headers: { 'Content-Type': 'application/json' },
        })
    }
}
